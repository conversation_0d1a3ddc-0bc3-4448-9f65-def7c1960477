{"name": "yunping", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "axios": "^1.7.3", "core-js": "^3.8.3", "echarts": "^5.5.1", "element-china-area-data": "^6.1.0", "element-ui": "^2.15.14", "postcss-plugin-px2rem": "^0.8.1", "vue": "^2.6.14", "vue-amap": "^0.5.10", "vue-countup-v2": "^4.0.0", "vue-echarts": "^7.0.2", "vue-router": "^3.5.1", "vue-webview": "^0.0.34", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "lib-flexible": "^0.3.2", "node-sass": "^9.0.0", "postcss-pxtorem": "^5.1.1", "sass-loader": "^16.0.0", "vue-template-compiler": "^2.6.14"}}