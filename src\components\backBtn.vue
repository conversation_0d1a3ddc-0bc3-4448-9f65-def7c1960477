<template>
  <div class="boxc">
    <div class="back_box" @click="toBack">
      <img class="back_img1" src="../assets/images/back_icon.png" alt="">
      <p class="t_name">返回</p>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    toBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
// 返回
.back_box {
  background: rgba(0, 0, 0, 0.44);
  position: absolute;
  right: 0;
  bottom: 0.625rem;
  color: white;
  border-radius: 1.25rem 0 0 1.25rem;
  display: flex;
  align-items: center;
  padding: 0.125rem 0.25rem 0.125rem 0.25rem;
  z-index: 2001;
  cursor: pointer;
  .back_img1 {
    width: 0.375rem;
    height: 0.375rem;
  }
  .t_name {
    font-size: 0.1875rem;
    margin-left: 0.125rem;
  }
}
</style>

