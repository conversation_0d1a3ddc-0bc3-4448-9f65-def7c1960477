<template>
  <div class="box">
    <div class="left_box">
      <div class="top_box">
        <img @click="goBack" class="jiantou_img" src="https://cdn.juesedao.cn/mdy/d0ccf550af4b44b99a3130636a26d23f" alt="">
        <div @click="goBack" class="title_box">关于欧萃鹰品牌</div>
        <div class="gang"></div>
      </div>
      <div class="left_c">
        <div class="one_box">
          <div class="a_box">
            <img src="https://cdn.juesedao.cn/mdy/747cb16ac980444eb2a2639e72f750a8" alt="">
          </div>
          <div class="a_box1">
            <p>OUCUIYING</p>
            <p>Home brand</p>
          </div>
        </div>
        <div class="two_box">
          <img :src="selectArr[activeId].content.img" alt="">
        </div>
        <div class="three_box">{{selectArr[activeId].content.cTitle}}</div>
        <div class="wz_text" v-for="(item1,index1) in selectArr[activeId].content.conText" :key="index1">{{item1}}</div>

      </div>
    </div>
    <div class="right_box">
      <div class="tou_box">
        <div class="xian_box">
          <div class="xian"></div>
        </div>
        <div class="xian_text">introduce all</div>
      </div>
      <div class="c_boxa">
        <div class="c_boxs" v-for="(item,index) in selectArr" :key="index" @click="toActive(item,index)">
          <div class="s_left_box">
            <div class="tt1 " :class="activeId == index ? 'active_text' : ''" v-for="(item1,index1) in item.eArr" :key="index1">{{item1}}</div>
            <div class="tt2 " :class="activeId == index ? 'active_text1' : ''">{{item.cName}}</div>
          </div>
          <div class="yuan_box">
            <div class="yuan" style="background:#000;" v-if="activeId == index">
              <img src="https://cdn.juesedao.cn/mdy/70798e788dca449281602e8bb1249aab" alt="">
            </div>
            <div class="yuan" v-else>
              <img src="https://cdn.juesedao.cn/mdy/2e38ee9b7d2f40e28ed98c2fdff9d14a" alt="">
            </div>
          </div>
        </div>
      </div>
    </div>
    <backBtn></backBtn>
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      selectArr: [
        {
          id: "1",
          cName: "关于欧萃鹰",
          eArr: ["About the OUCUIYING brand"],
          content: {
            img: "https://cdn.juesedao.cn/mdy/e18089fef2974863b50920e4f744ffb0",
            cTitle: "About OUCUIYING Brand Introduction",
            conText: [
              "欧萃鹰 OUCUIYING 一直致力打造家居产品采购服务与合作交流一站式平台，为国内高端消费群体提供世界顶级家居产品的跨境直购、物流及全产业链售后服务。",
              "OUCUI has been committed to building a one-stop platform for home product procurement services and cooperation exchanges, providing cross-border direct purchase, logistics and after-sales services of the world's top home products for domestic high-end consumer groups.",
              "凭借大湾区便利的物流实力和国际影响力，欧萃鹰 OUCUIYING 家居与来自多个国家和地区的上百家知名家居建材品牌建立了广泛而深入的业务关系，逐步形成成熟的产品供应体系。",
              "With the convenient logistics strength and international influence of the Greater Bay Area, OUCUIYING Home Furnishing has established extensive and in-depth business relationships with hundreds of well-known home building materials brands from many countries and regions, gradually forming a mature product supply system.",
            ],
          },
        },
        {
          id: "2",
          cName: "丰富的产品体系",
          eArr: ["Perfect a strong and rich", "product system"],
          content: {
            img: "https://cdn.juesedao.cn/mdy/beb57ec6733e403ba1559de0d6b4edc1",
            cTitle: "Perfect a strong and rich product system",
            conText: [
              "欧萃鹰 家居不断构建和完善强大而丰富的产品体系，产品种类丰富多样，涵盖了客厅、餐厅、卧室、书房、阳台、厨卫和户外等家居全场景的各式家具、岩板、瓷砖、洁具、灯具、橱柜以及饰品等高端家居建材产品，满足不同类型消费者个性化与多元化的购物体验和全宅软硬装高品质家居用品使用需求。",
              "OCUIYING Home Furnishing continues to build and improve a strong and rich product system, with a wide range of products, covering all kinds of furniture, slabs, tiles, sanitary ware, lamps, cabinets, and accessories in the living room, dining room, bedroom, study, balcony, kitchen and outdoor, etc. High-end home building materials products to meet the personalized and diverse shopping experience of different types of consumers and the use of high-quality home furnishings in the whole house.",
            ],
          },
        },
        {
          id: "3",
          cName: "严选全球，服务到家",
          eArr: ["Strictly selected globally,", "serving at home"],
          content: {
            img: "https://cdn.juesedao.cn/mdy/bba15cb02e404a5ab737c32426c3aad6",
            cTitle: "Strictly selected globally, serving at home",
            conText: [
              "严选全球，服务到家” 是欧萃鹰 OUCUIYING 家居的核心经营理念。坐拥集团强大的全球化运营团队综合实力，欧萃鹰 OUCUIYING家居在采购服务、物流运输、售后保障等方面具有差异化、特色化的服务优势。从货源优选、物流清关、送货上门、安装维修、产品护理等方面全程为消费者提供专业、贴心的五星级服务。",
              `Strict selection of the world, service at home" is the core business philosophy of OUCUIYING Home Furnishing. With the comprehensive strength of the group's strong global operation team, OUCUIYING Home Furnishing has differentiated and characteristic service advantages in procurement services, logistics and transportation, after-sales guarantee, etc. We provide consumers with professional and considerate five-star services throughout the process from the aspects of supply selection, logistics customs clearance, door-to-door delivery, installation and maintenance, and product care.`,
            ],
          },
        },
        {
          id: "4",
          cName: "国际化视野",
          eArr: ["high-end service"],
          content: {
            img: "https://cdn.juesedao.cn/mdy/40ebb437320c4efeb3f6b77ff5e2d0b6",
            cTitle: "high-end service",
            conText: [
              `欧萃鹰家居将致力与一众知名家居建材卖场以及国内高端会展平台合作，全面拓展高端设计师渠道，通过和数以万计的设计师、艺术家等群体开展深度合作，为消费者提供更专业、更多元的家居设计与软装搭配方案，让世界顶级家居产品在中国高阶家居生活场景中触手可及。欧萃鹰 将不断整合国际市场泛家居资源，以国际化视野和专业运营能力，为消费者提供更高性价比的高端家居产品以及更全面的优质服务。`,
              `OUCUIYING Home Furnishing will be committed to cooperating with a number of well-known home building materials stores and domestic high-end exhibition platforms to comprehensively expand the channels of high-end designers. Through in-depth cooperation with tens of thousands of designers, artists and other groups, it will provide consumers with more professional and diverse home design and soft decoration matching solutions, so that the world's top home products can be reached in China's high-end home life scenes. OUCUIYING will continue to integrate the international market's pan-home resources, with an international vision and professional operation capabilities, to provide consumers with higher cost-effective high-end home products and more comprehensive quality services.`,
            ],
          },
        },
      ],
      activeId: "0",
    };
  },
  components: {
    backBtn,
  },
  methods: {
    toActive(item, id) {
      this.activeId = id;
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  padding: 0.5rem 0.5rem 0 0.5rem;
  box-sizing: border-box;
  display: flex;
  .left_box {
    width: 11.25rem;
    // height: 3.75rem;
    border-right: 1px solid #e5e5e5;
  }
  .top_box {
    width: 100%;
    display: flex;
    align-items: center;
    .jiantou_img {
      width: 0.2188rem;
      height: 0.2188rem;
      cursor: pointer;
    }
    .title_box {
      font-size: 0.2rem;
      color: #434343;
      margin: 0 0.1125rem;
      font-weight: 400;
      cursor: pointer;
    }
    .gang {
      width: 1px;
      height: 0.1875rem;
      background: #434343;
    }
  }
  .left_c {
    width: 100%;
    height: 100%;
    padding: 0.375rem 0.75rem 0 0.625rem;
    box-sizing: border-box;
    .one_box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.125rem;
      .a_box {
        width: 0.475rem;
        // height: 0.275rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .a_box1 {
        font-size: 0.0875rem;
        color: #8b8d8b;
        text-transform: uppercase;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        font-family: "aaa";
      }
    }
    .two_box {
      width: 100%;
      //   height: 100%;
      margin-top: 0.0625rem;
      img {
        width: 100%;
        // height: 2.5rem;
      }
    }
    .three_box {
      font-size: 0.2938rem;
      text-transform: uppercase;
      font-weight: bold;
      margin-top: 0.1375rem;
      font-family: "aaa";
    }
    .wz_text {
      font-size: 0.125rem;
      color: #373737;
      margin-top: 0.25rem;
      font-family: "aaa";
    }
  }
  .right_box {
    width: 5rem;
    padding: 0.5rem 0 0.5rem 0.5rem;
    box-sizing: border-box;
    .tou_box {
      width: 100%;
      margin-top: 0.5313rem;
      margin-bottom: 0.0625rem;
      .xian_box {
        width: 100%;
        height: 2px;
        background: #e8e8e8;
        .xian {
          width: 0.4375rem;
          height: 2px;
          background: black;
        }
      }
      .xian_text {
        font-size: 0.0938rem;
        text-transform: uppercase;
        font-weight: 700;
        margin-top: 0.0625rem;
      }
    }
    .c_boxa {
      width: 100%;
      .c_boxs {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 0.625rem;
        cursor: pointer;
        .s_left_box {
          .tt1 {
            font-size: 0.1625rem;
            color: #6c6c6c;
            text-transform: uppercase;
            font-family: "aaa";
          }
          .tt2 {
            font-size: 0.1375rem;
            color: #6c6c6c;
            font-weight: 400;
            margin-top: 0.0625rem;
          }
          .active_text {
            font-size: 0.1812rem;
            font-weight: 700;
            color: black;
            text-decoration: underline;
            font-family: "aaa";
          }
          .active_text1 {
            font-size: 0.1375rem;
            color: black;
            font-family: "aaa";

            text-decoration: underline;
          }
        }
        .yuan_box {
          width: 0.5rem;
          height: 100%;
          .yuan {
            width: 0.3563rem;
            height: 0.3563rem;
            // background: black;
            border-radius: 3.75rem;
            border: 1px solid #6c6c6c;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              width: 0.15rem;
              height: 0.15rem;
            }
          }
        }
      }
    }
  }
}
</style>