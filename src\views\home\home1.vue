<template>
  <div class="app">
    <!-- <div id="amapcontainer" style="width: 100vw; height: 100vh"></div> -->
    <!-- <div class="left_box">
      <div class="logo_box">
        <img class="logo_img" src="../../assets/images/logo.png" alt="">
      </div>
      <div class="fun_box" @click="toLeftBtn(1)">
        <img class="icon_img1" src="../../assets/images/icon1.png" alt="">
        <p class="fun_title">看工地</p>
      </div>
      <div class="fun_box" @click="toLeftBtn(2)">
        <img class="icon_img1" src="../../assets/images/icon2.png" alt="">
        <p class="fun_title">看服务</p>
      </div>
      <div class="fun_box" @click="toLeftBtn(3)">
        <img class="icon_img1" src="../../assets/images/cube1.png" alt="">
        <p class="fun_title">看产品</p>
      </div>
      <div class="fun_box" @click="toLeftBtn(4)">
        <img class="icon_img1" src="../../assets/images/icon4.png" alt="">
        <p class="fun_title">看品牌</p>
      </div>
      <div class="fun_box" @click="toLeftBtn(5)">
        <img class="icon_img1" src="../../assets/images/icon5.png" alt="">
        <p class="fun_title">门店数据</p>
      </div>
    </div> -->
    <div class="left_box">
      <div class="logo_bb">
        <img class="img_ab" src="https://cdn.juesedao.cn/mdy/9b5a01066b5046d48e8dc98d01af6d07" alt="">
      </div>
      <div class="e_box">
        <div class="e_one">Design
          <img class="mar_img" src="https://cdn.juesedao.cn/mdy/69cadcae0dfa42efa8af0f8dbf654f05" alt="">
          <!-- <div class="mar_box">
          </div> -->
        </div>
        <div class="e_tow">Defines Life</div>
      </div>
      <!-- <div class="e_box1">Walking through our platform, you will encounter many internationally renowned furniture brands, </div> -->

      <div class="ps_box">
        <div class="ps_boxs" v-for="(item,index) in leftArr" :key="index" @click="toLeftBtn(item.id)">
          <img class="img" :src="item.img" alt="">
        </div>
      </div>
    </div>
    <div class="right_box">
      <!-- 头部 -->
      <div class="top_box">
        <div class="dengl" v-show="isLogin" @click="showBtns">
          <p>登录</p>
        </div>

        <div class="exit_img" v-if="!isLogin" @click="open">
          <img class="e_img" src="../../assets/images/exit.png" alt="">
        </div>

        <div class="title_text" v-if="!isLogin">
          <p class="p_title">{{userInfoData.record.storename}}</p>
        </div>

        <div class="select_box">
          <!-- <img class="s_img" src="../../assets/images/select.png" alt=""> -->
        </div>

        <div class="time_box">
          <p class="time_m">{{timeNum}}</p>
        </div>
      </div>

      <div class="content_box" :style="isShowa ? 'margin-top: 1.6875rem;' : ' margin-top: 3.3125rem;'">
        <!-- <div class="content_box"> -->
        <div class="cc_box">
          <div class="aa_box" v-for="(item,index) in rightArr" :key="index" @click="toDataList(item.id)">
            <img class="img_s" :src="item.img" alt="">
          </div>
        </div>
        <div v-if="isShowa" @click="toDataList(5)">
          <img style="width:100%;" src="https://cdn.juesedao.cn/mdy/6f46d676323444999166dafb601d51ee" alt="">
        </div>

      </div>

      <!-- 标题 -->
      <!-- <div class="title_box1">
        <div class="box_one">
          <img class="icon7" src="../../assets/images/icon7.png" alt="">
          <img class="icon6" src="../../assets/images/icon6.png" alt="">
        </div>
        <div class="box_two">
          <img class="text_img" src="../../assets/images/text.png" alt="">
        </div>
      </div> -->

      <!-- <div class="content_box">
        <div class="con_box1">
          <div class="img_a" @click="toDataList(2)" style="margin-bottom:20px;">
            <img class="img_aImg" src="../../assets/images/case1.png" alt="">
          </div>
          <div class="img_a" @click="toDataList(3)">
            <img class="img_aImg" src="../../assets/images/server.png" alt="">
          </div>
        </div>
        <div class="con_box2">
          <p class="title_a">寻找你心意的产品</p>
          <div class="p_bos">
            <div class="left1">
              <div class="min_box1" @click="toSelect(101)">
                <span class="span">阿尼玛</span>
              </div>
              <div class="min_box2" @click="toSelect(103)">
                <span class="span">曾建龙龙系列</span>

              </div>
            </div>
            <div class="left2">
              <div class="min_box1" @click="toSelect(122)">
                <span class="span">马毛3.0</span>
              </div>
              <div class="min_box2" @click="toSelect(99)">
                <span class="span">林学明L系列</span>
              </div>
              <div class="min_box3" @click="toSelect(104)">
                <span class="span">孟也M系列</span>
              </div>
            </div>
          </div>
        </div>
        <div class="con_box3" @click="toDataList(4)">
          <div class="mask_box">
            <img class="img_dit" src="../../assets/images/ditu.png" alt="">
          </div>
        </div>
      </div> -->
    </div>

    <el-dialog :visible.sync="dialogVisible" width="20%" top="30vh" :before-close="handleClose" :close-on-click-modal="false">
      <div class="user_login">
        <div>
          <i class="el-icon-user"></i>
          <input class="input_value" type="text" placeholder="请输入账号" v-model="form.userName" ref="userName" autocomplete="off" />
        </div>
        <div>
          <i class="el-icon-unlock"></i>
          <input class="input_value" type="password" v-model="form.passWord" placeholder="请输入密码" autocomplete="off" />
        </div>
        <div>
          <button class="to_login" @click="tapLogin">登录</button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AMapLoader from "@amap/amap-jsapi-loader";
import qs from "qs"; // 用来解决post请求格式问题
export default {
  data() {
    return {
      timer: "", // 定义一个定时器
      timeNum: "--:--",
      dialogVisible: false,
      userInfoData: "",
      isLogin: false,
      isShowa: false,
      form: {
        userName: "", //登录账号
        passWord: "", //登录密码
      },
      rightArr: [
        {
          id: "1",
          img: "https://cdn.juesedao.cn/mdy/111d4279424e4253933d240a982b5c29",
        },
        {
          id: "2",
          img: "https://cdn.juesedao.cn/mdy/b83b380c56f24bc89f61a5ee6991a8aa",
        },
        {
          id: "3",
          img: "https://cdn.juesedao.cn/mdy/dba377fa17174581a7f9d196450b5c45",
        },
        {
          id: "4",
          img: "https://cdn.juesedao.cn/mdy/36533a1509534b0796b37c7744dd1854",
        },
      ],
      leftArr: [
        {
          id: "1",
          img: "https://cdn.juesedao.cn/mdy/af2347befc8b41efaa0716052a2ba0d8",
        },
        {
          id: "2",
          img: "https://cdn.juesedao.cn/mdy/19682d09e8584b0c92409a026e7f02a5",
        },
        {
          id: "3",
          img: "https://cdn.juesedao.cn/mdy/cf8505b07aaf4cae9f7b50399ac3d378",
        },
        // {
        //   id: "4",
        //   img: "https://cdn.juesedao.cn/mdy/7bdcabaea5f34a3dbbe3b351fe64469f",
        // },
      ],
    };
  },
  methods: {
    handleClose(done) {
      done();
    },

    open() {
      this.$confirm("此操作将退出登录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        // type: 'warning'
      })
        .then(() => {
          //清除某一项
          // Cookies.remove('shopInfo') // value
          localStorage.removeItem("shopInfo1");
          this.dialogVisible = false; //登录框状态
          this.$message({
            type: "success",
            message: "退出成功",
          });
          setTimeout(() => {
            this.$router.go(0);
          }, 1000);
        })
        .catch(() => {});
    },

    showBtns() {
      this.dialogVisible = true;
    },

    //关闭蒙版
    hidden() {
      this.isMake = false;
    },
    // 用户点击登录
    tapLogin() {
      if (!this.form.userName) {
        this.$refs.userName.focus();
        this.$message({ message: "账号不能为空", type: "warning" });
        return false;
      }
      if (!this.form.passWord) {
        this.$message({ message: "密码不能为空", type: "warning" });
        return false;
      }
      let { userName, passWord } = this.form;
      // pwd = md5(passWord);
      let data = {
        dbName: "cloudscreen",
        userName,
        password: passWord,
      };
      console.log(data);
      this.$axios.get(`oe_cloudlogin_.csp`, { params: data }).then((res) => {
        if (res.code == "1") {
          localStorage.setItem("shopInfo1", JSON.stringify(res));
          // 有效时间为永久的cookie
          // Cookies.set('shopInfo', JSON.stringify(res), { expires: 9999 })
          this.isLogin = false; //隐藏登录按钮
          this.$message({ message: res.msg, type: "success" });
          setTimeout(() => {
            this.dialogVisible = false; //登录框状态
            // this.$router.go(0);
            this.$router.push({ path: "/dsa" });
          }, 1000);
        } else {
          this.$message({ message: res.msg, type: "error" });
        }
      });
    },

    toSelect(e) {
      this.$store.commit("addN", e);
      this.$router.push({ path: "/productList" });
    },

    toDataList(e) {
      let that = this;
      if (this.userInfoData) {
        if (e == "1") {
          this.$router.push({
            path: "/constructionList",
            query: { activeId: "2" },
          });
          // this.$router.push({ path: "/productList" });
          return false;
        } else if (e == "2") {
          this.$router.push({ path: "/caseList" });
          return false;
        } else if (e == "3") {
          this.$router.push({ path: "/serverPage" });
          // this.$router.push({ path: "/serverList" });

          return false;
        } else if (e == "4") {
          this.$router.push({ path: "/chooseBrand" });
          return false;
        } else if (e == "5") {
          this.$router.push({ path: "/dashboard" });
          return false;
        }
      } else {
        that.$message.error("请先登录");
      }
    },

    //跳转左侧导航栏
    toLeftBtn(e) {
      let that = this;
      if (this.userInfoData) {
        if (e == "1") {
          this.$router.push({
            path: "/productList",
            // query: { activeId: "2" },
          });
          return false;
        } else if (e == "2") {
          this.$router.push({
            path: "/PetrosalLuxury1",
          });
          return false;
        } else if (e == "3") {
          this.$router.push({ path: "/ouCuiList" });

          return false;
        } else if (e == "4") {
          this.$router.push({ path: "/listBrands" });
          // this.$router.push({ path: "/brand" });
          // window.location.href = "https://www.matistyle.com/";
          return false;
        } else if (e == "5") {
          this.$router.push({ path: "/dashboard" });
          return false;
        }
      } else {
        that.$message.error("请先登录");
      }
    },

    initAMap() {
      AMapLoader.reset();
      AMapLoader.load({
        key: "e18de717bc0db5e1faa219d669d00e05", // 申请好的Web端开发者Key，首次调用 load 时必填
        // version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: [
          "AMap.Scale",
          "AMap.ToolBar",
          "AMap.ControlBar",
          "AMap.Geocoder",
          "AMap.Marker",
          "AMap.CitySearch",
          "AMap.Geolocation",
          "AMap.AutoComplete",
          "AMap.InfoWindow",
        ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      })
        .then((AMap) => {
          // 获取到作为地图容器的DOM元素，创建地图实例
          this.map = new AMap.Map("amapcontainer", {
            //设置地图容器id
            // resizeEnable: true,
            zoom: "8", // 地图显示的缩放级别
            viewMode: "3D", // 使用3D视图
            zoomEnable: true, // 地图是否可缩放，默认值为true
            dragEnable: true, // 地图是否可通过鼠标拖拽平移，默认为true
            doubleClickZoom: true, // 地图是否可通过双击鼠标放大地图，默认为true
            // zoom: this.zoom, //初始化地图级别
            // center: [113.370824, 23.131265], // 初始化中心点坐标 广州
            // mapStyle: "amap://styles/darkblue", // 设置颜色底层
            content: '<div style="width:200px;">这里是点位信息</div>',
          });
          // 在图面添加工具条控件, 工具条控件只有缩放功能
          this.map.addControl(
            new AMap.ToolBar({
              liteStyle: true,
            })
          );
          //获取并展示当前城市信息

          this.setMapMarker();
        })
        .catch((e) => {
          console.log(e);
        });
    },

    getTime() {
      this.timer = setInterval(() => {
        // 获取当前时间的各个部分
        let timeDate = new Date();
        let hours = timeDate.getHours();
        // 格式化小时
        hours = hours >= 10 ? hours : "0" + hours;
        let minutes = timeDate.getMinutes();
        // 格式化分钟
        minutes = minutes >= 10 ? minutes : "0" + minutes;

        // 将获取的时间信息赋值给nowTime
        this.timeNum = `${hours}:${minutes}`;
      }, 1000); // 每隔一秒更新时间
    },
  },
  created() {
    // localStorage.setItem("shopInfo1", JSON.stringify(res));
    // if (JSON.parse(localStorage.getItem("shopInfo1"))) {
    //   localStorage.removeItem("shopInfo1");
    //   this.$router.go(0);
    // }
    this.getTime();
    // this.initAMap;
    if (localStorage.getItem("shopInfo1")) {
      this.userInfoData = JSON.parse(localStorage.getItem("shopInfo1"));
      if (this.userInfoData.record.phone == "4008929838") {
        this.isShowa = true;
      }
      //校验账号密码是否要重新登录
      // this.numberCheck();
      this.isLogin = false; //隐藏登录按钮
    } else {
      this.isLogin = true; //显示登录按钮
    }
  },
  //beforeDestroy是Vue组件的生命周期钩子之一，在组件销毁之前调用。在这里，它清除了之前设定的定时器，以避免内存泄漏。
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
};
</script>

<style lang="scss" scoped>
.app {
  width: 100vw;
  height: 100vh;
  background: url("../../assets/images/bg.png");
  background-size: cover; /* 覆盖整个元素 */
  background-repeat: no-repeat; /* 不重复图片 */
  background-position: center; /* 居中图片 */
  overflow: hidden;
  display: flex;
  .left_box {
    width: 9.5rem;
    // width: 12.5vw;
    height: 100vh;
    padding: 0.5rem 0.125rem 0.25rem 0.625rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    // align-items: center;
    margin-right: 0;
    .logo_bb {
      width: 1.5625rem;
      .img_ab {
        width: 100%;
      }
    }
    .e_box {
      // text-transform: uppercase;
      color: white;
      font-size: 0.9375rem;
      font-weight: bold;
      margin-top: 0.75rem;
      font-family: "aaa";
      .e_tow {
        margin-top: -0.1875rem;
      }
    }
    .e_box1 {
      width: 6.875rem;
      font-size: 0.2rem;
      color: white;
      font-weight: 400;
      margin: 0.0625rem 0 0.125rem;
      // font-family: "aaa";
    }
    .mar_box {
      width: 100%;
      margin: 0.25rem 0 0.1875rem;
    }
    .mar_img {
      width: 0.375rem;
      height: 0.375rem;
    }
    .ps_box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-top: 0.5rem;
      .ps_boxs {
        width: 2.8312rem;
        .img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .fun_box {
      // width: 110px;
      // height: 110px;
      // background: pink;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 0.7188rem;
      cursor: pointer;
      .icon_img1 {
        width: 0.375rem;
        // height: 2.625rem;
      }
      .fun_title {
        font-size: 0.175rem;
        color: #393e3e;
        margin-top: 0.0938rem;
      }
    }
  }
  .right_box {
    width: 5.9375rem;
    height: 100vh;
    padding: 0.0938rem 0 0 0;
    box-sizing: border-box;
    .top_box {
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      padding: 0.1875rem 0.3125rem 0 0;
      .exit_img {
        width: 0.3125rem;
        height: 0.3125rem;
        .e_img {
          width: 0.3125rem;
          height: 0.3125rem;
        }
      }
      .title_text {
        display: inline-block;
        margin: 0 0.125rem 0 0.25rem;
        .p_title {
          font-size: 0.1875rem;
          flex-wrap: 600;
          color: white;
        }
      }
      .select_box {
        width: 1px;
        height: 0.1875rem;
        background: white;
        border-radius: 46px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-right: 0.0625rem;
        .s_img {
          width: 0.1875rem;
          height: 0.1875rem;
        }
      }
      .time_box {
        font-size: 0.1875rem;
        margin: 0 0.3125rem 0 0;
        position: relative;
        .time_m {
          color: white;
          font-weight: 400;
        }
        ::after {
          content: "";
          width: 0.0105rem;
          height: 0.1875rem;
          background: #191a1c;
          position: absolute;
          left: -0.1875rem;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
    .content_box {
      width: 5.8125rem;
      // height: 5.625rem;
      border-radius: 0.125rem;
      background: rgba(246, 245, 242, 0.2);
      backdrop-filter: blur(2px);
      // margin-top: 2.625rem;
      padding: 0.25rem 0.1437rem;
      box-sizing: border-box;
      .cc_box {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
      }
      .aa_box {
        width: 100%;
        .img_s {
          width: 100%;
        }
      }
    }
  }

  ::v-deep .el-dialog {
    background-color: rgba(255, 255, 255, 0.9);
  }

  .user_login {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-content: center;
    box-sizing: border-box;
    // padding: 100px 30px;
    // background: white;
    & > div {
      margin: 0.125rem 0;
    }
    & > div:not(:last-child) {
      border-bottom: 1px solid lightgray;
      display: flex;
      align-items: flex-end;
      padding: 0.25rem 0;
    }
    i {
      color: #333;
      font-size: 18px;
      margin-right: 10px;
    }
    .to_login {
      border: none;
      background-color: rgba(255, 255, 255, 0.8);
      font-size: 0.1563rem;
      text-align: center;
      width: 100%;
      padding: 0.125rem 0;
      border-radius: 0.0125rem;
      cursor: pointer;
      letter-spacing: 0.125rem;
      text-indent: 0.0625rem;
      font-weight: bold;
    }

    input {
      border: none;
      outline-style: none;
      color: #333 !important;
      font-size: 14px;
      letter-spacing: 2px;
      width: 100%;
      background-color: rgba(255, 255, 255, -0.8);
    }

    input::-webkit-input-placeholder {
      color: #333;
    }
    ::-webkit-input-placeholder {
      /* WebKit browsers */
      color: lightgray;
    }
  }
  .dengl {
    width: 0.875rem;
    height: 0.375rem;
    background: rgba(255, 255, 255, 0.5);
    font-size: 0.1875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border-radius: 0.0625rem;
    color: #333;
  }
  .input_value {
    font-size: 0.0938rem;
    color: #333;
  }
}
</style>