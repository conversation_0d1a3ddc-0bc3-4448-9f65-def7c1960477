<template>
  <div class="app" v-loading="loading">
    <backBtn></backBtn>
    <div class="top_box">
      <img @click="goBack1" class="back_img" src="../../assets/images/back.png" alt="">
      <p class="top_title">产品列表</p>
      <p class="xian"></p>
    </div>
    <div class="content_box">
      <div class="left_box">
        <ul class="ul">
          <li class="li " :class="activeXilieId == item.id ? 'active_left' : ''" v-for="(item,index) in seriesDataList" :key="index" @click="xilBtn(item.id)">{{item.title}}</li>
          <!-- <li class="li">全部</li> -->
        </ul>
      </div>
      <div class="right_box">
        <div class="right_top">
          <div class="top_left">
            <ul class="ul">
              <li class="li" :class="item.id == guigeId ? 'active_a' : ''" v-for="(item,index) in specificationDataList" :key="index" @click="guigeBtn(item.id)">{{item.title}}</li>
            </ul>
            <ul class="ul">
              <li class="li" :class="item.id == colorId ? 'active_a' : ''" v-for="(item,index) in colorDataList" :key="index" @click="colorBtn(item.id)">{{item.title}}</li>
            </ul>
            <ul class="ul">
              <li class="li" :class="item.id == styleId ? 'active_a' : ''" v-for="(item,index) in touchDataList" :key="index" @click="touchBtn(item.id)">{{item.title}}</li>
            </ul>
          </div>
          <div class="top_right">
            <div class="input_box">
              <input type="text" v-model="keyword" @input="inputValue" class="input" placeholder="搜索产品信息">
              <img class="select_img" src="../../assets/images/select.png" alt="">
            </div>
          </div>
        </div>

        <div class="product_list" v-show="!dataNull">
          <div class="pro_boxs" v-for="(item,index) in productList" :key="index" @click="toDetalis(item.id)">
            <div class="img_box">
              <!-- <img class="cp_img" :src="item.productUrl" alt=""> -->
              <el-image class="cp_img" :src="item.productImage ? item.productImage : item.productUrl" fit="cover"></el-image>
            </div>
            <div class="cp_name">{{item.name}}</div>
            <div class="cp_gge">{{item.productModel}}</div>
          </div>
        </div>

        <div class="product_list1" v-show="dataNull">
          <el-empty description="暂无产品"></el-empty>
        </div>

        <div class="fenye_box">
          <el-pagination background hide-on-single-page @current-change="changePage" layout="prev, pager, next" :total="totalNum">
          </el-pagination>
        </div>

      </div>
    </div>
    <!-- 返回按钮 -->
    <!-- <div class="back_box" @click="goBack1">
      <img class="back_img1" src="../../assets/images/back_icon.png" alt="">
      <p class="t_name">返回</p>
    </div> -->
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      curPage: 1,
      productList: [], //产品列表
      totalNum: 1,
      seriesDataList: [],
      colorDataList: [],
      touchDataList: [],
      specificationDataList: [],

      activeXilieId: "",
      guigeId: "",
      colorId: "",
      styleId: "",
      loading: false,
      keyword: "",
      dataNull: false,
      selectActiveId: "",
      navActiveId: "",
    };
  },
  methods: {
    goBack1() {
      this.$router.go(-1);
    },

    //选中系列数据
    xilBtn(e) {
      this.$store.commit("addN", e);
      this.activeXilieId = this.$store.state.navSelectId;
      this.curPage = "1";
      this.productList = [];
      this.getDataList();
    },

    getToPageData(e) {
      this.curPage = "1";
      this.productList = [];
      this.getDataList();
    },

    changePage(e) {
      this.curPage = e;
      this.productList = [];
      this.getDataList();
    },

    //规格筛选
    guigeBtn(e) {
      this.productList = [];
      this.totalNum = 1;
      this.curPage = 1;
      if (this.styleId == e) {
        this.guigeId = "";
      } else {
        this.guigeId = e;
      }
      this.getDataList();
    },

    //色彩筛选
    colorBtn(e) {
      this.productList = [];
      this.totalNum = 1;
      this.curPage = 1;

      if (this.colorId == e) {
        this.colorId = "";
      } else {
        this.colorId = e;
      }
      this.getDataList();
    },
    //触感筛选
    touchBtn(e) {
      this.productList = [];
      this.totalNum = 1;
      this.curPage = 1;

      if (this.styleId == e) {
        this.styleId = "";
      } else {
        this.styleId = e;
      }
      this.getDataList();
    },

    //输入框事件
    inputValue() {
      // 清除已经设置的定时器
      clearTimeout(this.timeout);

      // 设置新的定时器
      this.timeout = setTimeout(() => {
        this.curPage = 1;
        this.productList = [];
        // 在这里处理输入框的逻辑
        this.getDataList();
      }, 300); // 设置防抖的时间，这里是300毫秒
    },

    //获取列表
    getDataList() {
      this.loading = true;
      this.$axios
        .get(`oe_productScreenMt_.csp`, {
          params: {
            dbName: "schender",
            // userId: this.userId,
            curPage: this.curPage,
            series: this.activeXilieId == "-1" ? "" : this.activeXilieId,
            size: this.guigeId,
            colour: this.colorId,
            style: this.styleId,
            keyword: this.keyword,
            // style: this.styleActiveId == "-1" ? "" : this.styleActiveId,
            // space: this.activeTypeId,
            // colour: this.activeAreaId,
          },
        })
        .then((res) => {
          this.loading = false;
          if (res.code == "1") {
            this.productList = res.records;
            this.totalNum = Number(res.totalNum);
            if (res.records.length <= "0" && this.productList.length <= "0") {
              this.dataNull = true;
            } else {
              this.dataNull = false;
            }
            // this.handleData(res.data[0].children);
            // this.typeDataList = res.data[0].children;
            // this.loading = false;
          }
        });
    },

    //获取列表
    getProdectType() {
      let that = this;

      this.$axios
        .get(`oe_queryProductTypeIt_.csp`, {
          params: {
            dbName: "schender",
          },
        })
        .then((res) => {
          if (res.code == "1") {
            res.data[0].children.forEach((item) => {
              if (item.title == "工艺") {
                item.children.unshift({ id: "-1", title: "全部" });
                this.seriesDataList = item.children;
                if (this.activeXilieId == "") {
                  this.activeXilieId = item.children[0].id;
                }
              } else if (item.title == "色彩") {
                this.colorDataList = item.children;
              } else if (item.title == "规格") {
                this.specificationDataList = item.children;
              } else if (item.title == "触感") {
                this.touchDataList = item.children;
              }
            });
            // if (this.activeXilieId > 0) {
            //   console.log("1111111111");
            //   that.getToPageData();
            // } else {
            //   console.log("222222222");
            //   that.getDataList();
            // }
          }
        });
    },

    toDetalis(id) {
      this.$router.push({ path: "/productDetails", query: { id: id } });
    },
  },

  components: {
    backBtn,
  },

  mounted() {
    let that = this;
    // this.activeXilieId = this.$store.state.navSelectId;
    that.getProdectType();
    that.getDataList();
  },
};
</script>

<style  lang="scss" scoped>
.app {
  width: 100%;
  height: 100%;
  padding: 0 0.25rem 0 0.125rem;
  box-sizing: border-box;
  .top_box {
    width: 100%;
    height: 8vh;
    display: flex;
    align-items: center;
    padding: 0.0625rem 0.25rem 0 0.25rem;
    box-sizing: border-box;

    .back_img {
      width: 0.2375rem;
      height: 0.2375rem;
      cursor: pointer;
    }
    .top_title {
      font-size: 0.2rem;
      color: #434343;
      margin: 0 0.125rem;
    }
    .xian {
      width: 1px;
      height: 0.2062rem;
      background: #434343;
    }
  }

  .content_box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .left_box {
      width: 1.25rem;
      height: 89vh;
      background: #f6f5f2;
      border-radius: 0.25rem;
      padding: 0.25rem 0.0125rem 0.125rem;
      box-sizing: border-box;
      overflow: hidden;
      .ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        overflow-y: auto;
        .li {
          list-style: none;
          font-size: 0.125rem;
          color: #838383;
          height: 0.625rem;
          cursor: pointer;
          // padding-bottom: 20px;
          margin-bottom: 0.25rem;
        }
        .active_left {
          font-size: 0.1375rem;
          font-weight: bold;
          color: #000000;
          position: relative;
        }
        .active_left::after {
          content: "";
          width: 0.1875rem;
          height: 0.025rem;
          border-radius: 0.25rem;
          background: black;
          position: absolute;
          left: 50%;
          bottom: --0.375rem;
          transform: translateX(-50%);
        }
      }
    }
    .right_box {
      width: 89%;
      height: 89vh;
      // background: #eef4fb;
      .right_top {
        width: 100%;
        height: 1rem;
        background: #f6f5f2;
        border-radius: 0.25rem;
        display: flex;
        align-items: center;
        padding: 0 0.25rem 0 0.125rem;
        box-sizing: border-box;
        .top_left {
          width: 82%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          .ul {
            display: flex;
            align-items: center;
            padding: 0.0625rem 0.125rem;
            overflow-x: auto;
            box-sizing: border-box;
            .li {
              list-style: none;
              padding-right: 0.25rem;
              margin-right: 0.1875rem;
              cursor: pointer;
              color: #8a8a8a;
              font-size: 0.125rem;
            }
            .active_a {
              font-weight: bold;
              color: black;
            }
            ::-webkit-scrollbar:vertical {
              width: 0.375rem;
            }
          }
        }
        .top_right {
          width: 18%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .input_box {
            width: 100%;
            height: 0.4375rem;
            display: flex;
            align-content: center;
            justify-content: flex-end;
            position: relative;
            .input {
              width: 2.125rem;
              height: 100%;
              background: #ffffff;
              border: none;
              border-radius: 2.875rem;
              font-size: 0.1275rem;
              padding: 0 0.4375rem 0 0.1875rem;
              box-sizing: border-box;
            }
            .select_img {
              width: 0.1875rem;
              height: 0.1875rem;
              position: absolute;
              top: 50%;
              right: 0.1875rem;
              transform: translateY(-50%);
              cursor: pointer;
            }
          }
        }
      }

      .product_list {
        width: 100%;
        height: 81%;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 0.1875rem;
        margin-top: 0.125rem;
        overflow-y: auto;
        .pro_boxs {
          width: 100%;
          cursor: pointer;
          .img_box {
            width: 100%;
            height: 1.875rem;
            border-radius: 0.0625rem;
            overflow: hidden;
            .cp_img {
              width: 100%;
              height: 100%;
            }
          }
          .cp_name {
            font-size: 0.1625rem;
            font-weight: bold;
            margin: 0.0625rem 0 0.0625rem 0;
          }
          .cp_gge {
            font-size: 0.1125rem;
            color: #202020;
          }
        }
      }

      .product_list1 {
        width: 100%;
        height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .fenye_box {
        width: 100%;
        // height: 3.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 0.0625rem;
      }
    }
  }

  /* 修改垂直滚动条 */
  ::-webkit-scrollbar {
    width: 0.0625rem; /* 修改宽度 */
  }

  /* 修改滚动条轨道背景色 */
  ::-webkit-scrollbar-track {
  }

  /* 修改滚动条滑块颜色 */
  ::-webkit-scrollbar-thumb {
    // background-color: #888;
    background-color: rgba(0, 0, 0, 0.1);
  }

  /* 修改滚动条滑块悬停时的颜色 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  /* 修改滚动条滑块移动时的颜色 */
  ::-webkit-scrollbar-thumb:active {
    background-color: #333;
  }

  /* 修改滚动条滑块的圆角 */
  ::-webkit-scrollbar-thumb {
    border-radius: 0.375rem;
  }
  //水平方向滚动条
  ::-webkit-scrollbar:horizontal {
    height: 0.0625rem;
  }
  // 返回
  .back_box {
    // width: 1.875rem;
    // height: 4.5625rem;
    background: rgba(0, 0, 0, 0.44);
    position: absolute;
    right: 0;
    top: 6.25rem;
    color: white;
    border-radius: 6.25rem 0 0 6.25rem;
    display: flex;
    align-items: center;
    padding: 0.125rem 0.25rem 0.125rem 0.125rem;
    z-index: 99999;
    cursor: pointer;
    .back_img1 {
      width: 0.375rem;
      height: 0.375rem;
    }
    .t_name {
      font-size: 0.1563rem;
      margin-left: 0.125rem;
    }
  }

  ::v-deep .el-pagination.is-background .el-pager li {
    margin: 0 0.0625rem !important;
  }

  ::v-deep .el-pagination.is-background .btn-next {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }
  ::v-deep .btn-prev {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }

  ::v-deep .el-pager li {
    font-size: 0.125rem;
    background-color: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }

  ::v-deep .el-image-viewer__mask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    background: #000;
  }
  ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: black;
  }
}
</style>