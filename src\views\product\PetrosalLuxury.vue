<template>
  <div class="app" v-loading="loading">
    <!-- <img class="bg_imga" src="https://cdn.juesedao.cn/mdy/274b840867314b8eb635fee3fb440c4b" alt=""> -->
    <backBtn></backBtn>
    <div class="top_box">
      <img @click="toBack" class="back_img" :src="topData.backImg" alt="">
      <p class="back_title">{{topData.title}}</p>
      <p class="xian"></p>
    </div>
    <div class="content_box">
      <div class="left_box">
        <ul class="ul">
          <li class="li" :class="activeXilieId == item.id ? 'active_left' : ''" v-for="(item,index) in typeData[0].children" :key="index" @click="xilBtn(item.id,index)">
            <span>{{item.cName}}</span>
            <!-- <span>{{item.eName}}</span> -->
          </li>
          <!-- <li class="li">全部</li> -->
        </ul>
      </div>
      <div class="right_box">
        <div class="right_top">
          <div class="top_left">
            <ul class="ul">
              <li class="li" :class="item.id == colorId ? 'active_a' : ''" v-for="(item,index) in rightTypeData" :key="index" @click="colorBtn(item)">{{item.name}}</li>
            </ul>
          </div>
          <div class="top_right">
            <div class="input_box">
              <input type="text" v-model="keyword" @input="inputValue" class="input" placeholder="搜索产品信息">
              <img class="select_img" src="../../assets/images/select.png" alt="">
            </div>
          </div>
        </div>

        <div class="product_list" v-show="!dataNull">
          <div class="pro_boxs" v-for="(item,index) in productList" :key="index" @click="toDetalis(item.id)">
            <div class="img_box">
              <!-- <img class="cp_img" :src="item.productUrl" alt=""> -->
              <el-image class="cp_img" :src="item.productUrl" fit="cover"></el-image>
            </div>
            <div class="cp_name">{{item.name}}</div>
            <div class="cp_gge">{{item.brandName}}</div>
          </div>
        </div>

        <div class="product_list1" v-show="dataNull">
          <el-empty description="暂无产品"></el-empty>
        </div>

        <div class="fenye_box">
          <el-pagination background hide-on-single-page @current-change="changePage" layout="prev, pager, next" :total="totalNum">
          </el-pagination>
        </div>

      </div>
    </div>
    <!-- 返回按钮 -->
    <!-- <div class="back_box" @click="goBack1">
      <img class="back_img1" src="../../assets/images/back_icon.png" alt="">
      <p class="t_name">返回</p>
    </div> -->
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      curPage: 1,
      productList: [], //产品列表
      totalNum: 1,
      seriesDataList: [],
      colorDataList: [],
      specificationDataList: [],
      activeXilieId: "",
      guigeId: "",
      colorId: "",
      loading: false,
      keyword: "",
      dataNull: false,
      selectActiveId: "",
      navActiveId: "",
      topData: {
        title: "产品列表",
        backImg: "https://cdn.juesedao.cn/mdy/e42a3bc1b0fc4dc2a6f56090e1022195",
      },
      typeData: [],
      typeIndex: 0,
      rightTypeData: [],
    };
  },
  methods: {
    toBack() {
      this.$router.go(-1);
    },

    //选中系列数据
    xilBtn(e, index) {
      console.log(e);
      // this.$store.commit("addN", e);
      this.activeXilieId = e;
      this.curPage = 1;
      this.totalNum = 0;
      this.productList = [];
      //   this.rightTypeData = e == "-999" ? "" : this.typeData[index].children;
      // this.colorId = e == "-999" ? "" : this.typeData[index].children[0].id;
      this.getDataList();
    },

    getToPageData(e) {
      this.curPage = "1";
      this.productList = [];
      this.getDataList();
    },

    changePage(e) {
      this.curPage = e;
      this.productList = [];
      this.getDataList();
    },

    //规格筛选
    guigeBtn(e) {
      this.productList = [];
      this.totalNum = 1;
      if (this.guigeId == e) {
        this.guigeId = "";
      } else {
        this.guigeId = e;
      }
      //   this.getDataList();
    },

    //色彩筛选
    colorBtn(e) {
      this.productList = [];
      this.totalNum = 1;
      this.curPage = 1;
      console.log(e.id);
      if (this.colorId == e.id) {
      } else {
        this.colorId = e.id;
      }
      this.getDataList();
    },

    //输入框事件
    inputValue() {
      // 清除已经设置的定时器
      clearTimeout(this.timeout);

      // 设置新的定时器
      this.timeout = setTimeout(() => {
        this.curPage = 1;
        this.totalNum = 0;
        this.productList = [];
        // 在这里处理输入框的逻辑
        this.getDataList();
      }, 300); // 设置防抖的时间，这里是300毫秒
    },

    //获取列表
    getDataList() {
      this.loading = true;
      this.$axios
        .get(`oe_getOucuiData_.csp`, {
          params: {
            code: "getProductList",
            curPage: this.curPage,
            series: this.activeXilieId == "-999" ? "" : this.activeXilieId,
            style: this.colorId == "-999" ? "" : this.colorId,
            keyword: this.keyword,
            // style: this.styleActiveId == "-1" ? "" : this.styleActiveId,
            // space: this.activeTypeId,
            // colour: this.activeAreaId,
          },
        })
        .then((res) => {
          this.loading = false;
          if (res.code == "1") {
            console.log(res);
            this.productList = res.result;
            this.totalNum = Number(res.totalNum);
            if (res.records.length <= "0" && this.productList.length <= "0") {
              this.dataNull = true;
            } else {
              this.dataNull = false;
            }
            // this.handleData(res.data[0].children);
            // this.typeDataList = res.data[0].children;
          } else {
            if (this.productList.length <= 0) {
              this.dataNull = true;
            }
          }
        });
    },

    //获取列表
    getProdectType() {
      this.loading = true;
      let that = this;
      this.$axios
        .get(`oe_getOucuiData_.csp`, {
          params: {
            code: "getProductTypeListYS",
          },
        })

        .then((res) => {
          this.loading = false;
          if (res.code == "1") {
            this.typeData = res.result[0].children;
            this.typeData[0].children.forEach((item) => {
              item.cName = this.splitStringAtFirstAlphabetic(
                item.name
              ).beforeArray;
              item.eName = this.splitStringAtFirstAlphabetic(
                item.name
              ).afterArray;
            });
            this.typeData[0].children.unshift({
              id: "-999",
              cName: "全部",
            });
            console.log(res.result[0].children, "---");
            this.activeXilieId = res.result[0].children[0].children[0].id;
            this.rightTypeData = this.typeData[1].children;
            this.rightTypeData.unshift({ name: "全部", id: "-999" });
            this.colorId = res.result[0].children[1].children[0].id;
          }
          this.getDataList();
        });
    },

    //跳转品牌产品详情
    toDetalis(id) {
      this.$router.push({ path: "/ysProductDetails", query: { id: id } });
    },

    //字符串根据第一个出现的英文切割
    splitStringAtFirstAlphabetic(str) {
      // 使用正则表达式找到第一个英文字母的下标
      const index = str.search(/[a-zA-Z]/);
      // 如果找到了英文字母
      if (index !== -1) {
        // 分割字符串成两个部分
        const beforeAlphabetic = str.slice(0, index);
        const afterAlphabetic = str.slice(index);
        // 将这两个部分转换成字符数组（如果需要的话）
        // 注意：这里转换成了字符数组，但你也可以保持它们为字符串
        const beforeArray = beforeAlphabetic.split("");
        const afterArray = afterAlphabetic.split("");
        // 返回结果，这里返回了一个包含两个数组的对象
        // 你可以根据需要调整返回格式
        return {
          beforeIndex: index,
          afterArray: afterArray.join(""),
          beforeArray: beforeArray.join(""),
          // 如果你只需要分割后的字符串，也可以返回这两个字符串
          // beforeString: beforeAlphabetic,
          // afterString: afterAlphabetic
        };
      } else {
        // 如果没有找到英文字母，返回 -1 和原始字符串的字符数组（或空数组）
        return {
          beforeIndex: -1,
          beforeArray: str.split("").filter(Boolean), // 去除可能的空字符串元素（虽然在这个场景下不会发生）
          afterArray: [], // 或者你可以返回原始字符串的字符数组，但标记为没有后部分
          // 或者简单地返回 { beforeIndex: -1, splitResult: [str.split('')] } 来表示未找到
        };
      }
    },

    // toDetalis(id) {
    //   this.$router.push({ path: "/brandDetails", query: { id: id } });
    // },
  },

  components: {
    backBtn,
  },

  mounted() {
    let that = this;
    // this.activeXilieId = this.$store.state.navSelectId;
    // that.getDataList();
  },
  created() {
    let that = this;
    that.getProdectType();
  },
};
</script>

<style  lang="scss" scoped>
.app {
  width: 100%;
  height: 100%;
  padding: 0 0.25rem 0 0.125rem;
  box-sizing: border-box;
  background: #fbfbfb;
  position: relative;
  .bg_imga {
    position: absolute;
    top: 0;
    right: 0;
    width: 6.9375rem;
  }
  .top_box {
    width: 100%;
    height: 7vh;
    display: flex;
    align-items: flex-end;
    padding: 0.0625rem 0.25rem 0 0.25rem;
    box-sizing: border-box;
    margin-bottom: 2vh;
    .back_img {
      width: 0.2375rem;
      height: 0.2375rem;
      cursor: pointer;
    }
    .back_title {
      font-size: 0.2rem;
      color: #434343;
      margin: 0 0.125rem;
    }
    .xian {
      width: 1px;
      height: 0.2062rem;
      background: #434343;
    }
  }

  .content_box {
    width: 100%;
    display: flex;
    .left_box {
      width: 0.9375rem;
      height: 89vh;
      background: #f6f5f2;
      border-radius: 0.25rem;
      padding: 0.25rem 0.0125rem 0.125rem;
      box-sizing: border-box;
      overflow: hidden;
      .ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        overflow-y: auto;
        .li {
          list-style: none;
          font-size: 0.125rem;
          color: #838383;
          // height: 3.125rem;
          cursor: pointer;
          padding-bottom: 20px;
          margin-bottom: 0.25rem;
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .active_left {
          font-size: 0.175rem;
          font-weight: bold;
          color: #000000;
          position: relative;
        }
        .active_left::after {
          content: "";
          width: 0.225rem;
          height: 0.025rem;
          border-radius: 0.25rem;
          background: black;
          position: absolute;
          left: 50%;
          bottom: 0.05rem;
          transform: translateX(-50%);
        }
      }
    }
    .right_box {
      width: 14.375rem;
      height: 89vh;
      // background: #eef4fb;
      .right_top {
        width: 100%;
        height: 0.75rem;
        background: #f6f5f2;
        border-radius: 0.25rem;
        display: flex;
        align-items: center;
        padding: 0 0.25rem 0 0.125rem;
        box-sizing: border-box;
        margin-left: 0.25rem;
        position: relative;
        .top_left {
          width: 82%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          .ul {
            display: flex;
            align-items: center;
            padding: 0.0625rem 0.125rem;
            overflow-x: auto;
            box-sizing: border-box;
            .li {
              list-style: none;
              padding-right: 0.25rem;
              margin-right: 0.1875rem;
              cursor: pointer;
              color: #8a8a8a;
              font-size: 0.125rem;
            }

            .li1 {
              list-style: none;
              font-size: 0.1062rem;
              color: #787777;
            }
            .active_a {
              font-weight: bold;
              color: black;
            }
            ::-webkit-scrollbar:vertical {
              width: 0.375rem;
            }
          }
        }
        .top_right {
          width: 18%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .input_box {
            width: 100%;
            height: 0.375rem;
            display: flex;
            align-content: center;
            justify-content: flex-end;
            position: relative;
            .input {
              width: 1.75rem;
              height: 100%;
              background: #ffffff;
              border: none;
              border-radius: 2.875rem;
              font-size: 0.1275rem;
              padding: 0 0.4375rem 0 0.1875rem;
              box-sizing: border-box;
            }
            .select_img {
              width: 0.1625rem;
              height: 0.1625rem;
              position: absolute;
              top: 50%;
              right: 0.1875rem;
              transform: translateY(-50%);
              cursor: pointer;
            }
          }
        }
      }

      .product_list {
        width: 14.375rem;
        // height: 81%;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 0.1875rem;
        margin-top: 0.125rem;
        margin-left: 0.25rem;
        overflow-y: auto;
        .pro_boxs {
          width: 100%;
          cursor: pointer;
          .img_box {
            width: 100%;
            height: 2.0625rem;
            border-radius: 0.0625rem;
            overflow: hidden;
            background: #f6f6f6;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.375rem 0.5rem;
            box-sizing: border-box;
            position: relative;
            z-index: 1;
            .cp_img {
              width: 70%;
              //   height: 100%;
            }
          }
          .cp_name {
            font-size: 0.15rem;
            font-weight: 400;
            margin: 0.0625rem 0 0 0;
          }
          .cp_gge {
            font-size: 0.1rem;
            color: #202020;
          }
        }
      }

      .product_list1 {
        width: 100%;
        height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .fenye_box {
        width: 100%;
        height: 1.25rem;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        margin-top: 0.125rem;
      }
    }
  }

  /* 修改垂直滚动条 */
  ::-webkit-scrollbar {
    width: 0.0625rem; /* 修改宽度 */
  }

  /* 修改滚动条轨道背景色 */
  ::-webkit-scrollbar-track {
  }

  /* 修改滚动条滑块颜色 */
  ::-webkit-scrollbar-thumb {
    // background-color: #888;
    background-color: rgba(0, 0, 0, 0.1);
  }

  /* 修改滚动条滑块悬停时的颜色 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  /* 修改滚动条滑块移动时的颜色 */
  ::-webkit-scrollbar-thumb:active {
    background-color: #333;
  }

  /* 修改滚动条滑块的圆角 */
  ::-webkit-scrollbar-thumb {
    border-radius: 0.375rem;
  }
  //水平方向滚动条
  ::-webkit-scrollbar:horizontal {
    height: 0.0625rem;
  }
  // 返回
  .back_box {
    // width: 1.875rem;
    // height: 4.5625rem;
    background: rgba(0, 0, 0, 0.44);
    position: absolute;
    right: 0;
    top: 6.25rem;
    color: white;
    border-radius: 6.25rem 0 0 6.25rem;
    display: flex;
    align-items: center;
    padding: 0.125rem 0.25rem 0.125rem 0.125rem;
    z-index: 99999;
    cursor: pointer;
    .back_img1 {
      width: 0.375rem;
      height: 0.375rem;
    }
    .t_name {
      font-size: 0.1563rem;
      margin-left: 0.125rem;
    }
  }

  ::v-deep .el-pagination.is-background .el-pager li {
    margin: 0 0.0625rem !important;
  }

  ::v-deep .el-pagination.is-background .btn-next {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }
  ::v-deep .btn-prev {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }

  ::v-deep .el-pager li {
    font-size: 0.125rem;
    background-color: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }

  ::v-deep .el-image-viewer__mask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    background: #000;
  }
  ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: black;
  }
}
</style>