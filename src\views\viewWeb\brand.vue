<template>
  <el-main>
    <backBtn></backBtn>
    <iframe :src="url" frameborder="0" width="100vw" :style="{height:calHeight}" scrolling="auto"></iframe>
  </el-main>
</template>
<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      url: "https://www.ocuiying.com/",
    };
  },
  components: {
    backBtn,
  },
  computed: {
    //计算属性 , 设置iframe高度为窗口高度少140px
    calHeight() {
      return window.innerHeight - 4 + "px";
    },
  },
};
</script>
<style lang='scss' scoped>
* {
  margin: 0;
  padding: 0;
}
iframe {
  width: 100%;
  overflow-x: hidden;
}

/* 自定义滚动条整体样式 */
::-webkit-scrollbar {
  width: 5px !important; /* 设置滚动条的宽度 */
  height: 0.375rem;
}

/* 自定义滚动条滑块样式 */
::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.27); /* 设置滑块的颜色 */
  border-radius: 0.3125rem;
}

/* 自定义滚动条轨道样式 */
::-webkit-scrollbar-track {
  background-color: #fff; /* 设置轨道的颜色 */
  border-radius: 0.3125rem;
}

.el-pagination .btn-prev,
.el-pagination .btn-next {
  background-color: #f5f5f5; /* 举例：更改分页按钮的背景颜色 */
}
</style>
