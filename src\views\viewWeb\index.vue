<template>
  <div>
    <backBtn></backBtn>
    <iframe :src="imageUrl" frameborder="0" width="100vw" :style="{height:calHeight}" scrolling="auto"></iframe>
  </div>
</template>  
  
<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      imageUrl: null,
    };
  },

  components: {
    backBtn,
  },
  computed: {
    //计算属性 , 设置iframe高度为窗口高度少140px
    calHeight() {
      return window.innerHeight - 4 + "px";
    },
  },

  created() {
    this.imageUrl = this.$route.query.url;
  },
};
</script>

<style lang='scss' scoped>
* {
  margin: 0;
  padding: 0;
}
iframe {
  width: 100%;
  overflow-x: hidden;
}
</style>


