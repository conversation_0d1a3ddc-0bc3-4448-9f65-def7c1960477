<template>
  <div class="app">
    <backBtn></backBtn>
    <div class="top_box">
      <img @click="toBack" class="back_img" src="../../assets/images/back.png" alt="">
      <p class="p1">了解服务</p>
    </div>
    <div class="con_box">
      <div class="left">
        <div class="select_box">
          <div class="left_btn" :class="activeId == '1' ? 'active_bg' : ''" @click="selectServer(1)">
            <img class="zt_img" v-if="activeId == '1'" src="https://cdn.juesedao.cn/mdy/ae22676d564547ba9e2d2bf621c86ace" alt="">
            <!-- <p class="p1" v-if="activeId == '2'">六好服务</p> -->
            <img v-if="activeId == '2'" class="no_text" src="https://cdn.juesedao.cn/mdy/f99d4aa53e7a4c1a81abd8dcd734a687" alt="">
          </div>
          <div class="right_btn" :class="activeId == '2' ? 'active_bg' : ''" @click="selectServer(2)">
            <img class="zt_img" v-if="activeId == '2'" src="https://cdn.juesedao.cn/mdy/5d15de8026de4c48a79974154b16e63d" alt="">
            <!-- <p class="p1" v-if="activeId == '1'">八大保障</p> -->
            <img v-if="activeId == '1'" class="no_text" src="https://cdn.juesedao.cn/mdy/559bb93b9e2e45ecaf73791db47dc63c" alt="">

          </div>
        </div>

        <div class="con_img_box" v-show="activeId == '1'">
          <div class="img_box1">
            <img class="img_one" src="https://cdn.juesedao.cn/mdy/dd35b783b8094d55bf5174681afa9a33" alt="">
          </div>
          <div class="img_box2">
            <img class="img_one" src="https://cdn.juesedao.cn/mdy/0709887e0d8d47498622a228ba516da4" alt="">
          </div>
        </div>
        <div class="con_img_box" v-show="activeId == '2'">
          <div class="img_box1" style="margin: 1.8% 0 1.2%;">
            <img class="img_one" src="https://cdn.juesedao.cn/mdy/99a21f802c8841059798ad7b47fb8eae" alt="">
          </div>
          <div class="img_box2">
            <img class="img_one" src="https://cdn.juesedao.cn/mdy/95a061fd7e7e4a9f8d80bac1f743405b" alt="">
          </div>
        </div>
      </div>
      <div class="right">
        <img class="ren_img" src="https://cdn.juesedao.cn/mdy/82e4fbb9dd864e958d450945be34b628" alt="">
        <!-- <img class="logo" src="../../assets/images/logo1.png" alt=""> -->
      </div>
      <div class="bottom_box" @click="toConstructionSite">
        <img class="bott_img" src="https://cdn.juesedao.cn/mdy/10c34fa1a8ce41388d441359bd1d212b" alt="">
        <div class="nr_box">
          <img class="qian_img" src="../../assets/images/firewall1.png" alt="">
          <p class="p">查看工地</p>
          <img class="qian_img1" src="../../assets/images/gdi_gd.png" alt="">

        </div>
      </div>
    </div>

    <img class="b1" v-show="activeId == '2'" src="https://cdn.juesedao.cn/mdy/d157188a58384f0288c02ff38069ac64" alt="">

  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";

export default {
  data() {
    return {
      activeId: "1",
    };
  },
  methods: {
    selectServer(e) {
      this.activeId = e;
    },
    //跳转工地列表
    toConstructionSite() {
      this.$router.push({
        path: "/constructionList",
        query: { activeId: "2" },
      });
    },
    toBack() {
      this.$router.go(-1);
    },
  },
  components: {
    backBtn,
  },
};
</script>
<style lang="scss" scoped>
.app {
  width: 100%;
  min-height: 100vh;
  position: relative;
  background: linear-gradient(180deg, #fff 0%, #e4edfc 55%, #f0f5fb 100%);
  .top_box {
    width: 100%;
    height: 8vh;
    // height: 0;
    display: flex;
    // align-items: center;
    padding: 0.2813rem 0.75rem 0.125rem;

    box-sizing: border-box;
    .back_img {
      width: 0.2813rem;
      height: 0.2813rem;
      cursor: pointer;
    }
    .p1 {
      font-size: 0.1875rem;
      color: #434343;
      margin-left: 0.0938rem;
      font-weight: bold;
      //   font-family: "LogoSC Unbounded Sans";
    }
  }
  .con_box {
    width: 100%;
    height: 100%;
    display: flex;
    .left {
      width: 73%;
      padding-left: 5%;
      .select_box {
        width: 68%;
        height: 12%;
        background: white;
        border-radius: 1.9375rem;
        border: 1px solid rgba(0, 0, 0, 0.16);
        box-sizing: border-box;
        display: flex;
        align-items: center;
        box-shadow: 0px 0px 5.5px 0px rgba(0, 0, 0, 0.16);
        margin-bottom: 0.25rem;
        .left_btn {
          width: 50%;
          height: 100%;

          border-radius: 1.9375rem;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          .zt_img {
            width: 44%;
          }
          .no_text {
            width: 38%;
          }
        }
        .right_btn {
          width: 50%;
          height: 100%;
          border-radius: 1.9375rem;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          .zt_img {
            width: 44%;
          }
          .p1 {
            font-size: 1.625rem;
            color: #575757;
            font-weight: bold;
          }
          .no_text {
            width: 38%;
          }
        }
        .active_bg {
          background: #3782f4;
        }
      }
      .con_img_box {
        width: 100%;
        margin: 0 auto;
        .img_box1 {
          width: 100%;
          margin: 1% 0 0.8% 0;
          .img_one {
            width: 95%;
          }
        }
        .img_box2 {
          width: 100%;
          .img_one {
            width: 95%;
          }
        }
      }
    }
    .right {
      width: 30%;
      height: 100%;
      position: fixed;
      right: 0;
      bottom: 0;
      // margin-top: 0.8187rem;
      display: flex;
      align-items: flex-end;
      .ren_img {
        width: 90%;
        margin-left: 0.5rem;
        // height: 100%;
      }
      .logo {
        width: 35%;
        position: absolute;
        top: 0.375rem;
        right: 0.9375rem;
      }
    }
    .bottom_box {
      width: 100%;
      height: 9%;
      //   height: 4.375rem;
      position: fixed;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 8;
      //   background: url("https://cdn.juesedao.cn/mdy/10c34fa1a8ce41388d441359bd1d212b");
      //   background-size: cover; /* 覆盖整个元素 */
      //   background-repeat: no-repeat; /* 不重复图片 */
      //   background-position: center; /* 居中图片 */
      .bott_img {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        z-index: -1;
      }
      .nr_box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .qian_img {
          width: 1.5%;
        }
        .qian_img1 {
          width: 1.1%;
        }
        .p {
          font-size: 0.1875rem;
          color: white;
          font-weight: bold;
          margin: 0 0.25rem;
        }
      }
    }
  }
  .b1 {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 64%;
    z-index: 0;
  }
}
</style>
