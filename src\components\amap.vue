<template>
  <div class="app">
    <div id="amapcontainer" style="width: 100vw; height: 100vh"></div>
    <div class="bottom_box">
      <div class="left" :class="activeId == '1' ? 'active_bg' : ''" @click="quehuanBtn(1)">
        <img class="img" src="https://cdn.juesedao.cn/mdy/d7887346ea3e4313bc95c8d0190b9141" alt="">
        <span>当前城市</span>
      </div>
      <div class="left" :class="activeId == '2' ? 'active_bg' : ''" @click="quehuanBtn(2)">
        <img class="img1" src="https://cdn.juesedao.cn/mdy/2b83db9bfd9043929626e76a520bc9e9" alt="">
        <span>全国城市</span>
      </div>
    </div>
  </div>
</template>
<script>
import AMapLoader from "@amap/amap-jsapi-loader";
import getLoadtion from "../../src/unit/unti";
// import location from "../../assets/positionLocation";
// import backBtn from "../../components/backBtn.vue";

window._AMapSecurityConfig = {
  securityJsCode: "e18de717bc0db5e1faa219d669d00e05", // '「申请的安全密钥」',
};
export default {
  data() {
    return {
      map: null,
      markerList: [],
      activeId: "2",
      zoom: "6",
      addOptions: [113.370824, 23.131265],
      mapList: [
        // {
        //   name: "小王",
        //   address: "广东省广州市海珠区",
        //   lnglats: [113.312566, 23.085073],
        // },
        // {
        //   name: "小张",
        //   address: "广东省广州市黄埔区",
        //   lnglats: [113.480794, 23.177896],
        // },
        // {
        //   name: "小李",
        //   address: "广东省广州市荔湾区",
        //   lnglats: [113.220556, 23.10718],
        // },
        // {
        //   name: "小赵",
        //   address: "广东省广州市天河区",
        //   lnglats: [113.365438, 23.124231],
        // },
      ],
    };
  },
  //   components: {
  //     backBtn,
  //   },
  mounted() {
    this.getGongdiList();
    // DOM初始化完成进行地图初始化
    // this.initAMap();
    // getLoadtion();
    this.getDizi();
  },
  methods: {
    //切换地区
    quehuanBtn(e) {
      this.activeId = e;
      if (e == "1") {
        // this.getLoadtion();
        // this.getAddress();
        this.getDizi();
        this.zoom = "12";
        this.initAMap();
      } else {
        this.zoom = "5";
        this.initAMap();
      }
    },

    getDizi() {
      let that = this;
      let aMapScript = document.createElement("script");
      aMapScript.setAttribute(
        "src",
        "https://webapi.amap.com/maps?v=1.4.11&key=e18de717bc0db5e1faa219d669d00e05&plugin=AMap.CitySearch"
      );
      document.head.appendChild(aMapScript);
      aMapScript.onload = function () {
        AMap.plugin("AMap.Geolocation", function () {
          var geolocation = new AMap.Geolocation({
            // 是否使用高精度定位，默认：true
            enableHighAccuracy: true,
            // 设置定位超时时间，默认：无穷大
            timeout: 10000,
            // 定位按钮的停靠位置的偏移量，默认：Pixel(10, 20)
            buttonOffset: new AMap.Pixel(10, 20),
            //  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
            zoomToAccuracy: true,
            //  定位按钮的排放位置,  RB表示右下
            buttonPosition: "RB",
          });

          geolocation.getCurrentPosition();
          AMap.event.addListener(geolocation, "complete", onComplete);
          AMap.event.addListener(geolocation, "error", onError);

          function onComplete(data) {
            // data是具体的定位信息
            console.log(data);
            console.log("123123");
            that.addOptions[0] = data.position.lng; // that.getAddress(data.position.lng, data.position.lat);
            that.addOptions[1] = data.position.lat; // that.getAddress(data.position.lng, data.position.lat);
          }

          function onError(data) {
            console.log(data);
            // 定位出错
            console.log("123123s");
          }
        });
      };
    },

    // 地址逆解析
    getAddress(lng = "113.1752712", lat = "23.0212518") {
      this.$axios
        .get(`https://restapi.amap.com/v3/geocode/regeo`, {
          location: lng,
          lat,
          key: "6020e8d9d674d24bb921047dff8bde05",
          radius: 1000,
          extensions: "all",
        })
        .then((res) => {
          console.log(res);
        });
      // request({
      //   url: `https://restapi.amap.com/v3/geocode/regeo?location=${lng},${lat}&key=6020e8d9d674d24bb921047dff8bde05)}&radius=1000&extensions=all`,
      //   method: "get",
      // }).then((res) => {
      //   if (res.status == "1") {
      //     console.log("res", res);
      //     // this.addmark({ lng, lat, name: res.regeocode.formatted_address });
      //   } else {
      //     this.$message({ message: res.info, type: "error" });
      //   }
      // });
    },

    addmark(info) {
      if (this.marker) this.mapEl.remove(this.marker);
      this.currentAddr = info;
      this.marker = new this.aMap.Marker({
        position: new this.aMap.LngLat(info.lng, info.lat),
        title: info.name, // 鼠标滑过点标记时的文字提示
        label: {
          content: info.name, // 文本标注的内容
        },
        // zooms: 14 // 点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]
      });
      this.mapEl.add(this.marker);
      this.mapEl.setCenter([info.lng, info.lat], false, 500); // 中心点 是否直接迁移(动画过度) 过渡时间
    },

    //跳转详情
    toDetails(id) {
      this.$router.push({ path: "/constructionDetails", query: { id: id } });
    },

    //获取工地数据
    getGongdiList() {
      // this.loading = true;
      this.$axios
        .get(`oe_getProjects_.csp`, {
          params: {
            dbName: this.$dbName,
          },
        })
        .then((res) => {
          // this.loading = false;
          console.log(res);
          if (res.code == "1") {
            res.records.forEach((item) => {
              let arr = [item.storeLongitude];
              arr.push(item.storeLatitude);
              item.lnglats = arr;
              item.name = item.community;
            });
            this.mapList = res.records;
            // setTimeout(() => {
            this.initAMap();
            // }, 2000);
            // this.totalNum = res.totalNum;
          }
        });
    },

    logMapinfo() {},

    //获取当前位置
    getLoadtion() {
      //   console.log("111");
      //   let _that = this;
      //   let geolocation = location.initMap("map-container"); // 定位
      //   console.log(geolocation);
      //   AMap.event.addListener(geolocation, "complete", (result) => {
      //     // _that.lat = result.position.lat;
      //     // _that.lng = result.position.lng;
      //     // _that.location = result.formattedAddress;
      //   });
    },

    initAMap() {
      AMapLoader.reset();
      AMapLoader.load({
        key: "e18de717bc0db5e1faa219d669d00e05", // 申请好的Web端开发者Key，首次调用 load 时必填
        // version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: [
          "AMap.Scale",
          "AMap.ToolBar",
          "AMap.ControlBar",
          "AMap.Geocoder",
          "AMap.Marker",
          "AMap.CitySearch",
          "AMap.Geolocation",
          "AMap.AutoComplete",
          "AMap.InfoWindow",
        ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      })
        .then((AMap) => {
          // 获取到作为地图容器的DOM元素，创建地图实例
          this.map = new AMap.Map("amapcontainer", {
            //设置地图容器id
            // resizeEnable: true,
            // zoom: 8,
            viewMode: "3D", // 使用3D视图
            zoomEnable: true, // 地图是否可缩放，默认值为true
            dragEnable: true, // 地图是否可通过鼠标拖拽平移，默认为true
            doubleClickZoom: true, // 地图是否可通过双击鼠标放大地图，默认为true
            zoom: this.zoom, //初始化地图级别
            center: this.addOptions, // 初始化中心点坐标 广州
            // mapStyle: "amap://styles/darkblue", // 设置颜色底层
          });
          // this.map.setZoom(5);
          // 在图面添加工具条控件, 工具条控件只有缩放功能
          this.map.addControl(
            new AMap.ToolBar({
              liteStyle: true,
            })
          );
          //获取并展示当前城市信息

          this.setMapMarker();
        })
        .catch((e) => {
          console.log(e);
        });
    },
    // 增加点标记
    setMapMarker() {
      let that = this;
      // 创建 AMap.Icon 实例
      let icon = new AMap.Icon({
        size: new AMap.Size(36, 36), // 图标尺寸
        image: "https://cdn.juesedao.cn/mdy/1c9282c24f974aea9fbfb92f4d5d21ed", // Icon的图像
        imageSize: new AMap.Size(28, 28), // 根据所设置的大小拉伸或压缩图片
        imageOffset: new AMap.Pixel(0, 0), // 图像相对展示区域的偏移量，适于雪碧图等
      });
      let makerList = [];
      this.mapList.forEach((item) => {
        // 遍历生成多个标记点
        let marker = new AMap.Marker({
          map: this.map,
          zIndex: 9999999,
          icon: icon, // 添加 Icon 实例
          offset: new AMap.Pixel(-13, -30), //icon中心点的偏移量
          position: item.lnglats, // 经纬度对象new AMap.LngLat(x, y)，也可以是经纬度构成的一维数组[116.39, 39.9]
        });

        // 设置label标签
        // label默认蓝框白底左上角显示，样式className为：amap-marker-label
        marker.setLabel({
          // 修改label相对于maker的位置
          offset: new AMap.Pixel(-110, -70), //点标记定位

          content: `<div class="marker" data-projectid="${item.id}">
           <div class="box1">
              <img style="width:50%; heigth:100%;" src="https://cdn.juesedao.cn/mdy/cfc218409f244b929d666dc36c9f696d" alt="">
             </div>
             <div class="title_nameaa">${item.name}</div>
        </div> `,
        });
        // marker.on("click", (mapEvent) => {
        //   console.log(mapEvent);
        //   console.log(
        //     mapEvent.target.dom
        //       .getElementsByClassName("marker")[0]
        //       .getAttribute("data-projectid")
        //   );
        //   console.log(mapEvent.target);
        // });
        marker.setExtData({ projectId: item.id }); // 设置自定义属性
        marker.on("click", (mapEvent) => {
          this.toDetails(mapEvent.target.getExtData().projectId); // 访问自定义属性
          // console.log();
        });
        // marker.on("click", (e) => {
        //   console.log(e);
        // });
        marker.setMap(that.map);
        makerList.push(marker);
      });

      this.map.add(makerList);
      // 自动适应显示想显示的范围区域
      // this.map.setFitView();
    },
  },
};
</script>
<style lang="scss" scoped>
.app {
  width: 100%;
  height: 100%;

  .bottom_box {
    width: 4.375rem;
    height: 0.875rem;
    position: fixed;
    left: 50%;
    bottom: 0.625rem;
    transform: translateX(-50%);
    background: #cacaca;
    border-radius: 2.5rem;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    overflow: hidden;

    .left {
      width: 100%;
      height: 100%;
      cursor: pointer;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.1875rem;
      .img {
        width: 0.25rem;
        margin-right: 0.1875rem;
      }
      .img1 {
        width: 0.3125rem;
        margin-right: 0.1875rem;
      }
    }
    .active_bg {
      background-color: black;
      color: white;
    }
  }
  ::v-deep .amap-marker-label {
    // padding: 2px 6px;
    border-radius: 0;
    background: rgba(0, 0, 0, 0) !important;
    text-align: center;
    font-size: 14px;
    border: none !important;
  }
  ::v-deep .marker {
    // padding: 0 0.125rem;
    width: 1.875rem;
    height: 0.4375rem;
    background: #8b644c;
    border-radius: 1.25rem 1.25rem 1.25rem 0;
    font-size: 0.0625rem;
    cursor: pointer;
    display: flex;
    align-content: center;
    border: 0.0063rem solid white;
    .title_nameaa {
      width: 1.875rem;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 0.0625rem;
      box-sizing: border-box;
      font-size: 0.1125rem;
      font-weight: 600;
      color: white;
      white-space: nowrap; /* 确保文本在一行内显示 */
      overflow: hidden; /* 超出容器部分隐藏 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }
    .box1 {
      width: 0.625rem;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-right: 1px solid white;
      box-sizing: border-box;
    }
  }
}
</style>
