<template>
  <div class="app" v-loading="loading">
    <backBtn></backBtn>
    <div class="top_box">
      <img class="back_img" @click="toBack()" src="../../assets/images/back.png" alt="">
      <p class="back_title">案例详情</p>
      <p class="xian"></p>
    </div>

    <div class="con_box1">
      <div class="left_box1">
        <div class="title_a">{{caseDetails.name}}</div>
        <div class="info_box">
          <div class="info">
            <img class="info_img" src="../../assets/images/Location_pin.png" alt="">
            <p class="info_text">{{caseDetails.chengshi}}</p>
          </div>
          <div class="info">
            <img class="info_img" src="https://cdn.juesedao.cn/mdy/ee204f6514164af487d2273d866f2c9a" alt="">
            <p class="info_text" v-if="caseDetails.shuxing">{{caseDetails.shuxing}}㎡</p>
            <p class="info_text" v-else>暂无</p>
          </div>
          <div class="info">
            <img class="info_img" src="https://cdn.juesedao.cn/mdy/f9034e0873ff406a83aaae09b5688298" alt="">
            <p class="info_text" v-if="caseDetails.space">{{caseDetails.space}}</p>
            <p class="info_text" v-else>暂无</p>
          </div>
        </div>
        <div class="jianjie_box">
          <div class="jianjie_title">案例简介</div>
          <div class="nr_box" v-html="caseDetails.caseDetail"></div>
        </div>
        <div class="product_box">
          <div class="box_a1">
            <div class="box_s_p" v-for="(item,index) in caseDetails.product" :key="index" @click="toProductDetails(item.id)">
              <el-image style="width: 100%; height: 90%" :src="item.productUrl2" fit="fit"></el-image>
              <p class="pp_names">{{item.name}}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="right_box1">
        <div class="img_box2">
          <el-carousel :interval="5000" height="100%" style=" height:100%; " arrow="always">
            <el-carousel-item style="width:100%; height:100%; " v-for="(item,index) in caseDetails.photo" :key="index">
              <el-image style="width: 100%; height: 100%" :src="item.url" fit="contain" :preview-src-list="imgXguoArr">
              </el-image>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>

    <div class="con_box2">

    </div>
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      caseId: "",
      caseDetails: "",
      imgXguoArr: [],
    };
  },
  components: {
    backBtn,
  },
  methods: {
    toBack() {
      this.$router.go(-1);
    },
    //获取列表
    getDataList() {
      this.loading = true;
      let that = this;
      this.$axios
        .get(`oe_queryAllCaseItemSed_.csp`, {
          params: {
            dbName: "schender",
            id: this.caseId,
          },
        })
        .then((res) => {
          this.loading = false;
          if (res.code == "1") {
            if (res.records[0].photo && that.isJson(res.records[0].photo)) {
              res.records[0].photo = JSON.parse(res.records[0].photo);
            }
            if (res.records[0].photo) {
              res.records[0].photo.forEach((item) => {
                that.imgXguoArr.push(item.url);
              });
            }
            that.caseDetails = res.records[0];
            console.log(that.caseDetails);
          }
        });
    },

    //跳转产品详情
    toProductDetails(id) {
      this.$router.push({ path: "/productDetails", query: { id: id } });
    },

    isJson(str) {
      try {
        JSON.parse(str);
      } catch (e) {
        return false;
      }
      return true;
    },
  },
  created() {
    this.caseId = this.$route.query.id;
    this.getDataList();
  },
};
</script>

<style lang="scss" scoped>
.app {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #fff 0%, #f6f5f2 55%, #f6f5f2 100%);
  overflow: hidden;
  .ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .top_box {
    width: 100%;
    height: 9vh;
    display: flex;
    align-items: center;
    padding: 0.0625rem 0.25rem 0.0625rem 0.375rem;
    box-sizing: border-box;
    margin-top: 0.125rem;
    .back_img {
      width: 0.2375rem;
      height: 0.2375rem;
      cursor: pointer;
    }
    .back_title {
      font-size: 0.2rem;
      color: #434343;
      margin: 0 0.125rem;
    }
    .xian {
      width: 1px;
      height: 0.2062rem;
      background: #434343;
    }
  }

  .con_box1 {
    width: 95%;
    // height: ;
    margin: 0 auto;
    display: flex;
    .left_box1 {
      width: 29%;
      height: 100%;
      padding: 0.375rem 0.375rem 0 0;
      box-sizing: border-box;
      overflow-y: auto;
      font-family: "aaa";
      .title_a {
        font-size: 0.25rem;
        color: #262626;
        font-weight: bold;
      }
      .info_box {
        width: 100%;
        display: flex;
        align-items: center;
        margin-top: 0.375rem;
        .info {
          width: 33%;
          display: flex;
          align-items: center;
          .info_img {
            width: 0.1875rem;
            height: 0.1875rem;
          }
          .info_text {
            font-size: 0.1062rem;
            margin-left: 0.0625rem;
          }
        }
      }
      .product_box {
        width: 100%;
        height: 3.125rem;
        display: flex;
        overflow-x: auto;
        .box_a1 {
          display: flex;
          white-space: nowrap; /* 确保子元素不换行 */
          .box_s_p {
            width: 1.25rem;
            height: 100%;
            // background: red;
            margin-right: 0.1875rem;
            overflow: hidden;
          }
          .pp_names {
            width: 100%;
            white-space: nowrap; /* 保证文本在一行内显示 */
            overflow: hidden; /* 超出容器部分隐藏 */
            text-overflow: ellipsis;
          }
        }
      }
      .jianjie_box {
        width: 100%;
        margin: 0.375rem 0;
        .jianjie_title {
          font-size: 0.1563rem;
        }
        .nr_box {
          font-size: 0.125rem;
          color: #000000;
          margin-top: 0.25rem;
        }
      }
    }

    .right_box1 {
      width: 72%;
      height: 100%;
      overflow: hidden;
      .img_box2 {
        width: 100%;
        height: 7.5rem;
        .img_s {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  /* 自定义滚动条整体样式 */
  ::-webkit-scrollbar {
    width: 5px !important; /* 设置滚动条的宽度 */
    height: 0.0625rem;
  }

  /* 自定义滚动条滑块样式 */
  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.27); /* 设置滑块的颜色 */
    border-radius: 0.3125rem;
  }

  /* 自定义滚动条轨道样式 */
  ::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, 0.67); /* 设置轨道的颜色 */
    border-radius: 0.3125rem;
  }
}
</style>
