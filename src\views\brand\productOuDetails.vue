<template>
  <div class="app" v-loading="loading">
    <backBtn></backBtn>
    <div class="left_box">
      <div class="top_back">
        <img @click="toBack" class="back_img" src="../../assets/images/back.png" alt="">
        <p class="p1">产品详情</p>
      </div>
      <div class="zhong_box">
        <div class="name_a">
          <p>{{productDetails.name}}</p>
        </div>
        <div class="atout_box">
          <div class="boxs_1">
            <div class="img_hez">
              <img class="logo_aa" :src="productDetails.brandRecord.logo" alt="">
            </div>
            <!-- <el-image class="logo_aa" :src="productDetails.brandRecord.logo" fit="scale-down"> </el-image> -->
          </div>
          <div class="boxs_1">
            <div class="box_12">
              <img class="icon1" src="https://cdn.juesedao.cn/mdy/80ae6cbc450a41c189aef57f7b2cd568" alt="">
              <p class="title_a1">产地</p>
            </div>
            <p class="title_a">{{productDetails.brandRecord.brandPlace}}</p>
          </div>
          <div class="boxs_1">
            <div class="box_12">
              <img class="icon1" src="https://cdn.juesedao.cn/mdy/f0c079173fd04b77bc32d888bf219121" alt="">
              <p class="title_a1">设计师</p>
            </div>
            <p class="title_a">{{productDetails.brandRecord.stylist || '暂无'}}</p>
          </div>
          <div class="boxs_1">
            <div class="box_12">
              <img class="icon1" src="https://cdn.juesedao.cn/mdy/3bed90fc15dd40d7afd7d640c9742418" alt="">
              <p class="title_a1">年份</p>
            </div>
            <p class="title_a">{{productDetails.brandRecord.productYear || '暂无'}}</p>
          </div>
        </div>
        <div class="product_list" style="grid-template-columns: repeat(1, 1fr)">
          <div class="product_boxs" v-for="(item,index) in sizeArr" :key="index">
            <div class="cp_img" :style="isZoom ? 'margin-bottom: -3rem; transform: rotate(90deg);' : ''">
              <!-- <img class="imgs" :src="item" alt=""> -->
              <el-image class="imgs" :style="isZoom ? 'width: 19%;' : ''" :src="item" fit="contain" :preview-src-list="sizeArr"></el-image>
            </div>
            <!-- <div class="cp_name">{{productDetails.productModel}}</div> -->
          </div>
        </div>

      </div>

    </div>
    <div class="right_box">
      <el-carousel :interval="5000" v-show="!isNull" height="100%" style=" height:100%; " arrow="always">
        <el-carousel-item style="width:100%; height:100%; " v-for="(item,index) in productDetails.productImages" :key="index">
          <el-image style="width: 100%; height: 100%" :src="item.url" fit="cover" :preview-src-list="imgXguoArr">
          </el-image>
        </el-carousel-item>
      </el-carousel>

      <div class="null_box" v-show="isNull">
        <el-empty description="暂无效果图"></el-empty>
      </div>

      <!-- 二维码 -->
      <!-- <div class="qr_img">
        <el-image class="qr" :src="productDetails.qrImage" fit="cover" :preview-src-list="qrArrImg"></el-image>
      </div> -->
      <!-- 相关功能 -->
      <!-- <div class="fun_boxs">
        <div class="f_boxs" @click="toFunBtn(1)">
          <img class="f_img" src="../../assets/images/720.png" alt="">
          <p class="title">720°全景</p>
          <div class="pop_up" v-if="VrShow">此产品暂无相关VR</div>
        </div>
        <div class="f_boxs" @click="toFunBtn(2)">
          <img class="f_img1" src="../../assets/images/yuan1.png" alt="">
          <p class="title">相关案例</p>
          <div class="pop_up" v-if="anliShow">此产品暂无相关案例</div>
        </div>
        <div class="f_boxs" @click="toFunBtn(3)">
          <img class="f_img2" src="../../assets/images/gongdi.png" alt="">
          <p class="title">相关工地</p>
          <div class="pop_up" v-if="gongdiShow">此产品暂无相关工地</div>

        </div>
      </div> -->
      <!-- 返回按钮 -->
      <!-- <div class="back_box" @click="toBack">
        <img class="back_img1" src="../../assets/images/back_icon.png" alt="">
        <p class="t_name">返回</p>
      </div> -->
    </div>
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      productId: "",
      curPage: 1,
      productDetails: "",
      loading: false,
      srcList: [],
      isNull: true,
      imgXguoArr: [],
      qrArrImg: [],
      VrShow: false,
      anliShow: false,
      gongdiShow: false,
      guigeImgArr: [],
      guigeArr: ["200*1200", "250*1500", "150*900"],
      isZoom: false,
      brandRecord: "",
      sizeArr: [],
    };
  },
  components: {
    backBtn,
  },
  methods: {
    toBack() {
      this.$router.go(-1);
    },

    //跳转功能按钮
    toFunBtn(e) {
      if (e == "1") {
        //720
        if (this.productDetails.productVr) {
        } else {
          this.VrShow = true;
          setTimeout(() => {
            this.VrShow = false;
          }, 2500);
        }
        return false;
      } else if (e == "2") {
        //相关案例
        this.anliShow = true;
        setTimeout(() => {
          this.anliShow = false;
        }, 2500);
        // if()
        // this.$router.push({ path: "/relatedCases" });
        return false;
      } else {
        this.gongdiShow = true;
        setTimeout(() => {
          this.gongdiShow = false;
        }, 2500);
        // this.$router.push({ path: "/relatedConstructionSites" });
        //相关工地
      }
    },

    //获取列表
    getDataList() {
      this.loading = true;
      let that = this;
      this.$axios
        .get(`oe_getOucuiData_.csp`, {
          params: {
            code: "getBrandGoods",
            id: this.productId,
          },
        })
        .then((res) => {
          this.loading = false;
          if (res.code == "1") {
            if (this.isJson(res.result.productImages)) {
              res.result.productImages = JSON.parse(res.result.productImages);
            }
            if (this.isJson(res.result.productSize)) {
              res.result.productSize = JSON.parse(res.result.productSize);
            }
            console.log(res);

            // res.records[0].productGuige.forEach((item) => {
            //   if (this.isJson(item.images)) {
            //     item.images = JSON.parse(item.images);
            //     item.images.forEach((item1) => {
            //       this.guigeImgArr.push(item1.url);
            //     });
            //     if (
            //       res.records[0].sizename == this.guigeArr[0] ||
            //       res.records[0].sizename == this.guigeArr[1] ||
            //       res.records[0].sizename == this.guigeArr[2]
            //     ) {
            //       // this.datadragEnd(item.images[0].url);
            //       // 创建实例对象
            //       var img = new Image();
            //       // 图片地址
            //       img.src = item.images[0].url;
            //       img.onload = function () {
            //         if (img.width < img.height) {
            //           console.log("111");
            //           that.isZoom = true;
            //         }
            //       };
            //     }
            //   }
            // });
            // if (this.isJson(res.records[0].productImages)) {
            //   res.records[0].productImages = JSON.parse(
            //     res.records[0].productImages
            //   );
            this.isNull = false;

            res.result.productImages.forEach((item1) => {
              this.imgXguoArr.push(item1.url);
            });
            res.result.productSize.forEach((item1) => {
              this.sizeArr.push(item1.url);
            });
            // }
            this.productDetails = res.result;
            // this.brandRecord = res.brandRecord;
          }
        });
    },

    datadragEnd(url) {},

    //判断是否为json类型

    isJson(item) {
      if (typeof item !== "string") {
        return false;
      }
      try {
        JSON.parse(item);
        return true;
      } catch (e) {
        return false;
      }
    },
  },
  created() {
    this.productId = this.$route.query.id;
    this.getDataList();
  },
};
</script>

<style lang="scss" scoped>
.app {
  width: 100%;
  display: flex;
  .left_box {
    width: 26%;
    max-height: 100vh;
    background-image: linear-gradient(#fff 60%, #f6f5f2 100%);
    padding: 0.125rem 0;

    .top_back {
      width: 100%;
      height: 6vh;
      padding: 0 0.375rem;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      .back_img {
        width: 0.2188rem;
        height: 0.2188rem;
        cursor: pointer;
      }
      .p1 {
        font-size: 0.2rem;
        margin-left: 0.1125rem;
        color: #434343;
      }
    }

    .zhong_box {
      width: 96%;
      max-height: 92vh;
      //   height: 18.75rem;
      background: white;
      border-radius: 0 0.375rem 0.375rem 0;
      padding: 0.1875rem 0.25rem 0.1875rem 0.375rem;
      box-sizing: border-box;
      // box-sizing: border-box;
      overflow-y: auto;
      .atout_box {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        .boxs_1 {
          width: 50%;
          height: 0.5rem;
          padding-right: 0.25rem;
          box-sizing: border-box;
          margin: 0.125rem 0 0.25rem;
          .img_hez {
            width: 0.375rem;
            height: 0.375rem;
          }
          .icon1 {
            width: 0.125rem;
            height: 0.125rem;
            margin-right: 0.0625rem;
          }
          .title_a {
            font-size: 0.125rem;
            color: #2b2b2b;
            margin-top: 0.0625rem;
          }
          .title_a1 {
            font-size: 0.125rem;
            color: #5a5a5a;
          }
        }
      }
      .product_list {
        width: 100%;
        // height: 2.9375rem;
        display: grid;
        background: white;

        // overflow-x: auto;
        .product_boxs {
          // width: 8.75rem;
          height: 100%;
          margin-right: 0.0625rem;

          .cp_name {
            font-size: 0.125rem;
            color: #171717;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .cp_img {
            width: 100%;

            // zoom: 0.1;
            // height: 2.625rem;

            .imgs {
              width: 100%;
              // height: 2.5rem;
            }
          }
        }
      }
    }

    .guige {
      width: 100%;
      padding: 0 0.375rem 0 0;
      box-sizing: border-box;
      margin-top: 0.1875rem;
      margin-bottom: 0.375rem;
      .span1 {
        padding: 0.0625rem 0.1875rem;
        background: rgba(0, 0, 0, 0.27);
        font-size: 0.125rem;
        color: white;
        border-radius: 1.25rem;
      }
    }
    .name_a {
      font-size: 0.25rem;
      color: #262626;
      font-weight: 400;
      padding: 0 0.375rem 0 0;
      box-sizing: border-box;
      margin-bottom: 0.375rem;
      // padding: 0 0.375rem;
      // box-sizing: border-box;
    }

    /* 自定义滚动条整体样式 */
    ::-webkit-scrollbar {
      width: 5px !important; /* 设置滚动条的宽度 */
      height: 0.0625rem;
    }

    /* 自定义滚动条滑块样式 */
    ::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.27); /* 设置滑块的颜色 */
      border-radius: 0.3125rem;
    }

    /* 自定义滚动条轨道样式 */
    ::-webkit-scrollbar-track {
      background-color: #fff; /* 设置轨道的颜色 */
      border-radius: 0.3125rem;
    }
  }

  .right_box {
    width: 74%;
    height: 100vh;
    overflow: hidden;
    position: relative;
    .el-carousel__item h3 {
      color: #475669;
      font-size: 18px;
      opacity: 0.75;
      // line-height: 300px;
      margin: 0;
    }

    .right_img {
      width: 100%;
      height: 100%;
    }
    .qr_img {
      width: 1.25rem;
      height: 1.25rem;
      border-radius: 11.375rem;
      overflow: hidden;
      position: absolute;
      top: 0.1875rem;
      right: 0.1875rem;
      z-index: 99;
      .qr {
        width: 100%;
        height: 100%;
      }
    }
    .fun_boxs {
      width: 0.875rem;
      height: 3.125rem;
      background: rgba(0, 0, 0, 0.34);
      position: absolute;
      // top: 2.125rem;
      top: 50%;
      right: 0.25rem;
      transform: translateY(-50%);
      border-radius: 1.875rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
      padding: 0.1875rem 0;
      box-sizing: border-box;
      z-index: 998;
      .f_boxs {
        // width: ;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        cursor: pointer;
        .f_img {
          width: 0.375rem;
          height: 0.375rem;
        }
        .f_img1 {
          width: 0.3125rem;
          height: 0.3125rem;
        }
        .f_img2 {
          width: 0.3125rem;
          height: 0.3125rem;
        }
        .title {
          font-size: 0.125rem;
          color: white;
          margin-top: 0.0625rem;
        }
        .pop_up {
          height: 0.375rem;
          width: 1.35rem;
          background-color: #fff;
          position: absolute;
          left: -1.575rem;
          top: 50%;
          transform: translateY(-50%);
          z-index: 999;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 0.125rem;
          box-shadow: 0 0 1rem 0 rgba(0, 0, 0, 0.5);
          font-size: 0.125rem;
          transition: all 0.8s;
          // position: absolute;
          // left: 0;
          // top: 0;
        }
        .pop_up::after {
          content: "";
          position: absolute;
          right: -0.25rem;
          top: 50%;
          transform: translate(0, -50%);
          border-top: 0.0625rem solid transparent;
          border-right: 0.0625rem solid transparent;
          border-bottom: 0.0625rem solid transparent;
          border-left: 0.25rem solid #fff;
          z-index: 99;
        }
      }
    }

    // 返回
    .back_box {
      // width: 1.875rem;
      // height: 4.5625rem;
      background: rgba(0, 0, 0, 0.44);
      position: absolute;
      right: 0;
      top: 6.25rem;
      color: white;
      border-radius: 6.25rem 0 0 6.25rem;
      display: flex;
      align-items: center;
      padding: 0.125rem 0.25rem 0.125rem 0.125rem;
      z-index: 99999;
      cursor: pointer;
      .back_img1 {
        width: 0.375rem;
        height: 0.375rem;
      }
      .t_name {
        font-size: 0.125rem;
        margin-left: 0.125rem;
      }
    }
  }
  .null_box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-carousel__arro {
    right: 6.25rem !important;
  }

  ::v-deep .el-carousel__arrow {
    border: none;
    outline: 0;
    padding: 0;
    margin: 0;
    height: 36px;
    width: 36px;
    cursor: pointer;
    transition: 0.3s;
    border-radius: 50%;
    background-color: rgba(31, 45, 61, 0.51);
    color: #fff;
    position: absolute;
    top: 50%;
    // right: 1.25rem !important;
    z-index: 10;
    transform: translateY(-50%);
    text-align: center;
    font-size: 12px;
  }
}
.boxs_2 {
  width: 0.6375rem;
  //   height: 0.6375rem;
}
.box_12 {
  display: flex;
  align-items: center;
}

::v-deep .el-image-viewer__wrapper {
  /* 修改背景色为半透明黑色 */
  // background-color: rgba(0, 0, 0, 1);
  opacity: 1 !important;
  /* 其他样式修改 */
  /* 例如：边框样式、边距等 */
}
.logo_aa {
  width: 100%;
  // height: 0.5rem;
  // height: 100%;
}
</style>

