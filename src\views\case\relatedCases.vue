<template>
  <div class="app">
    <div class="top_back">
      <img @click="toBack" class="back_img" src="../../assets/images/back.png" alt="">
      <p class="p1">相关案例</p>
    </div>
    <div class="xg_title">城市砂岩珍珠 CITYSTONE PEARL 相关案例</div>
    <div class="case_box">
      <div class="bos_box" v-for="(item,index) in 11" :key="index">
        <div class="img_box">
          <img class="img_aa" src="https://cdn.juesedao.cn/mdy/ceb9c15762e24df0bb3a2e6096fb01b1" alt="">
        </div>
        <div class="bos_title">现代原木风</div>
      </div>
    </div>
    <div class="back_box" @click="toBack">
      <img class="back_img1" src="../../assets/images/back_icon.png" alt="">
      <p class="t_name">返回</p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    toBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.app {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #fff 0%, #e4edfc 55%, #f0f5fb 100%);
  .top_back {
    width: 100%;
    padding: 0.25rem 0.5rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .back_img {
      width: 0.4375rem;
      height: 0.4375rem;
      cursor: pointer;
    }
    .p1 {
      font-size: 0.3125rem;
      margin-left: 0.0625rem;
      color: #434343;
    }
  }
}
.xg_title {
  width: 100%;
  padding: 0 0.5rem 0.125rem 0.4875rem;
  box-sizing: border-box;
  font-size: 0.1625rem;
  color: #262626;
}
.case_box {
  width: 100%;
  height: 79vh;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.1625rem;
  padding: 0 0.4375rem;
  box-sizing: border-box;
  margin: 0.0625rem 0;
  .bos_box {
    width: 100%;
    // height: 15.625rem;
    // margin-bottom: 1.875rem;
    cursor: pointer;
    .img_box {
      width: 100%;
      height: 2.125rem;
      .img_aa {
        width: 100%;
        height: 100%;
      }
    }
    .bos_title {
      font-size: 0.1375rem;
      font-weight: 600;
      margin-top: 0.05rem;
    }
  }
}
// 返回
.back_box {
  // width: 9.4375rem;
  // height: 4.5625rem;
  background: rgba(0, 0, 0, 0.44);
  position: absolute;
  right: 0;
  top: 6.25rem;
  color: white;
  border-radius: 6.25rem 0 0 6.25rem;
  display: flex;
  align-items: center;
  padding: 0.125rem 0.25rem 0.125rem 0.25rem;
  cursor: pointer;
  .back_img1 {
    width: 0.5rem;
    height: 0.5rem;
  }
  .t_name {
    font-size: 0.25rem;
    margin-left: 0.0625rem;
  }
}
</style>

