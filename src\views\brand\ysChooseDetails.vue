<template>
  <div class="box">
    <div class="left_box">
      <div class="top_box">
        <img @click="goBack" class="jiantou_img" src="https://cdn.juesedao.cn/mdy/d0ccf550af4b44b99a3130636a26d23f" alt="">
        <div @click="goBack" class="title_box">关于岩奢高定</div>
        <div class="gang"></div>
      </div>
      <div class="left_c">
        <div class="one_box">
          <div class="a_box">
            <img src="https://cdn.juesedao.cn/mdy/82c2850529c349068bbe320a2c62eb35" alt="">
          </div>
          <div class="a_box1">
          </div>
        </div>
        <div class="two_box">
          <img :src="selectArr[activeId].content.img" alt="">
        </div>
        <div class="three_box">{{selectArr[activeId].content.cTitle}}</div>
        <div class="wz_text" v-for="(item1,index1) in selectArr[activeId].content.conText" :key="index1">{{item1}}</div>

      </div>
    </div>
    <div class="right_box">
      <div class="tou_box">
        <div class="xian_box">
          <div class="xian"></div>
        </div>
        <div class="xian_text">introduce all</div>
      </div>
      <div class="c_boxa">
        <div class="c_boxs" v-for="(item,index) in selectArr" :key="index" @click="toActive(item,index)">
          <div class="s_left_box">
            <div class="tt1 " :class="activeId == index ? 'active_text' : ''" v-for="(item1,index1) in item.eArr" :key="index1">{{item1}}</div>
            <div class="tt2 " :class="activeId == index ? 'active_text1' : ''">{{item.cName}}</div>
          </div>
          <div class="yuan_box">
            <div class="yuan" style="background:#000;" v-if="activeId == index">
              <img src="https://cdn.juesedao.cn/mdy/70798e788dca449281602e8bb1249aab" alt="">
            </div>
            <div class="yuan" v-else>
              <img src="https://cdn.juesedao.cn/mdy/2e38ee9b7d2f40e28ed98c2fdff9d14a" alt="">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="box1">
      <div class="back_box" @click="goBack">
        <img class="back_img1" src="../../assets/images/back_icon.png" alt="">
        <p class="t_name">返回</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectArr: [
        {
          id: "1",
          cName: "关于品牌",
          eArr: ["About SCHENDER HOME"],
          content: {
            img: "https://cdn.juesedao.cn/mdy/6e9acf41ee534a3c8bd3008efdd2f2f1",
            cTitle: "About SCHENDER HOME",
            conText: [
              "从全宅整体空间解决方案倡导者，向意式高奢定制交付领导者进阶施恩德一直致力于为高端用户提供岩板家居系统配套应用定制，",
              "From an advocate of overall space solutions for the whole house to a leader in the delivery of Italian high-luxury customization, Advanced Schende has been committed to providing high-end users with rock slab home system supporting application customization.",
              "SCHENDER施恩德全岩家居定制涵盖客厅系统、卧室系统、入户系统、厨卫系统、门墙系统和复合外墙系统等，在岩板家具成品研发设计上，推出顶奢级别的岩板餐桌、岩板茶几和岩板边几，具有很高的艺术观赏性与实用功能性。",
              "schender whole-house slab home customizalioncovers living room systems, bedroom systems,eniry systems, kitchen and bathroom systems, door and wall systems and composite exterior wall systems, etcin terms of research and design of the slabfurniture,schender has launched top-luxurydining tables, coffee tables and side tables, whichare highly artistic and functional,",
            ],
          },
        },
        {
          id: "2",
          cName: "岩奢定制家居",
          eArr: ["Rock luxury custom home"],
          content: {
            img: "https://cdn.juesedao.cn/mdy/027849b36faf407fbc721ef460f63069",
            cTitle: "Rock luxury custom home",
            conText: [
              "岩奢定制家居源自意大利顶级岩板家具设计团队原创设计。设计师以超前国际视野和艺术前瞻性，融汇奢石岩板的质感将SCHENDER施恩德品牌高奢定制的理念融入客户日常生活中，为全世界追求前卫优雅、极简艺术的用户提供时尚艺术精品。",
              "Rock luxury custom home comes from the original design of Italy's top rock slab furniture design team. With an advanced international vision and artistic foresight, the designer integrates the texture of luxury stone slabs to integrate the concept of high luxury customization of the SCHENDER brand into the daily life of customers, providing fashion art boutiques for users around the world who pursue avant-garde elegance and minimalist art.",
            ],
          },
        },
      ],
      activeId: "0",
    };
  },
  methods: {
    toActive(item, id) {
      this.activeId = id;
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  padding: 0.5rem 0.5rem 0 0.5rem;
  box-sizing: border-box;
  display: flex;
  .left_box {
    width: 11.25rem;
    // height: 3.75rem;
    border-right: 1px solid #e5e5e5;
  }
  .top_box {
    width: 100%;
    display: flex;
    align-items: center;
    .jiantou_img {
      width: 0.2188rem;
      height: 0.2188rem;
      cursor: pointer;
    }
    .title_box {
      font-size: 0.2rem;
      color: #434343;
      margin: 0 0.1125rem;
      font-weight: 400;
      cursor: pointer;
    }
    .gang {
      width: 1px;
      height: 0.1875rem;
      background: #434343;
    }
  }
  .left_c {
    width: 100%;
    height: 100%;
    padding: 0.375rem 0.75rem 0 0.625rem;
    box-sizing: border-box;
    .one_box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.125rem;
      .a_box {
        width: 0.8125rem;
        // height: 0.275rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .a_box1 {
        font-size: 0.0875rem;
        color: #8b8d8b;
        text-transform: uppercase;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        font-family: "aaa";
      }
    }
    .two_box {
      width: 100%;
      //   height: 100%;
      margin-top: 0.0625rem;
      img {
        width: 100%;
        // height: 2.5rem;
      }
    }
    .three_box {
      font-size: 0.2938rem;
      text-transform: uppercase;
      font-weight: bold;
      margin-top: 0.1375rem;
      font-family: "aaa";
    }
    .wz_text {
      font-size: 0.125rem;
      color: #373737;
      margin-top: 0.25rem;
      font-family: "aaa";
    }
  }
  .right_box {
    width: 5rem;
    padding: 0.5rem 0 0.5rem 0.5rem;
    box-sizing: border-box;
    .tou_box {
      width: 100%;
      margin-top: 0.5313rem;
      margin-bottom: 0.0625rem;
      .xian_box {
        width: 100%;
        height: 2px;
        background: #e8e8e8;
        .xian {
          width: 0.4375rem;
          height: 2px;
          background: black;
        }
      }
      .xian_text {
        font-size: 0.0938rem;
        text-transform: uppercase;
        font-weight: 700;
        margin-top: 0.0625rem;
      }
    }
    .c_boxa {
      width: 100%;
      .c_boxs {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 0.625rem;
        cursor: pointer;
        .s_left_box {
          .tt1 {
            font-size: 0.1625rem;
            color: #6c6c6c;
            text-transform: uppercase;
            font-family: "aaa";
          }
          .tt2 {
            font-size: 0.1375rem;
            color: #6c6c6c;
            font-weight: 400;
            margin-top: 0.0625rem;
          }
          .active_text {
            font-size: 0.1812rem;
            font-weight: 700;
            color: black;
            text-decoration: underline;
          }
          .active_text1 {
            font-size: 0.1375rem;
            color: black;
            font-family: "aaa";

            text-decoration: underline;
          }
        }
        .yuan_box {
          width: 0.5rem;
          height: 100%;
          .yuan {
            width: 0.3563rem;
            height: 0.3563rem;
            // background: black;
            border-radius: 3.75rem;
            border: 1px solid #6c6c6c;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              width: 0.15rem;
              height: 0.15rem;
            }
          }
        }
      }
    }
  }
  // 返回
  .back_box {
    background: rgba(0, 0, 0, 0.44);
    position: absolute;
    right: 0;
    bottom: 0.625rem;
    color: white;
    border-radius: 1.25rem 0 0 1.25rem;
    display: flex;
    align-items: center;
    padding: 0.125rem 0.25rem 0.125rem 0.25rem;
    z-index: 2001;
    cursor: pointer;
    .back_img1 {
      width: 0.375rem;
      height: 0.375rem;
    }
    .t_name {
      font-size: 0.1875rem;
      margin-left: 0.125rem;
    }
  }
}
</style>