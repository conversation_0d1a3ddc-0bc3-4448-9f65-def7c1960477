<template>
  <div class="app" v-loading="loading">
    <backBtn></backBtn>
    <div class="top_box">
      <img @click="goBack" class="back_img" src="../../assets/images/back.png" alt="">
      <p class="back_title">看品牌</p>
      <p class="xian"></p>
    </div>
    <div class="content_box">
      <div class="yw_text">Choose your brand</div>
      <p class="pp_name_a">选择品牌</p>
      <div class="pp_box">
        <div class="pp_boxs" @click="toPpxq(2)">
          <div class="boxs_logo_img">
            <img class="imga" src="https://cdn.juesedao.cn/mdy/a3fc79b5a61344e2962914f3ba09eada" alt="">
          </div>
          <div class="pp_tu_box">
            <img class="zhong_img" src="https://cdn.juesedao.cn/mdy/4389e18d53f94231b40e051c75df54d0" alt="">
          </div>
          <div class="pp_mingz">SCHENDER施恩德岩板</div>
        </div>
        <div class="pp_boxs" @click="toPpxq(3)">
          <div class="boxs_logo_img">
            <img class="imga" src="https://cdn.juesedao.cn/mdy/82c2850529c349068bbe320a2c62eb35" alt="">
          </div>
          <div class="pp_tu_box">
            <img class="zhong_img" src="https://cdn.juesedao.cn/mdy/678a1cc395504f8b997290bd7402a674" alt="">
          </div>
          <div class="pp_mingz">SCHENDER施恩德岩奢高定</div>
        </div>
        <div class="pp_boxs" @click="toPpxq(1)">
          <div class="boxs_logo_img">
            <img class="imga1" src="https://cdn.juesedao.cn/mdy/e9d753fce5324b37978d1b88448bd02e" alt="">
          </div>
          <div class="pp_tu_box">
            <img class="zhong_img" src="https://cdn.juesedao.cn/mdy/537cdac3b4274b1896a649547b7649bd" alt="">
          </div>
          <div class="pp_mingz">OCUIYING欧萃鹰精选</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      userId: "1",
      curPage: "1",
      caseDataList: [], //案例数据
      totalNum: 0,
      styleActiveId: "", //风格选中id
      activeTypeId: "", //户型选中id
      activeAreaId: "", //户型选中id
      styleData: [], //风格
      typeData: [], //户型
      areaData: [], //面积
      loading: false,
      keywork: "",
      timeout: null,
      dataNull: false,
    };
  },
  components: {
    backBtn,
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    //跳转详情
    toPpxq(e) {
      if (e == "1") {
        this.$router.push({ path: "/chooseDetails" });
        return false;
      } else if (e == "2") {
        this.$router.push({ path: "/endChooseDetails" });
        return false;
      } else if (e == "3") {
        this.$router.push({ path: "/ysChooseDetails" });
        return false;
      }
    },

    //点击切换风格分类
    styleBtn(e) {
      this.styleActiveId = e;
      ((this.caseDataList = []), (this.curPage = 1)), this.getDataList();
    },
    //点击切换风格分类
    typeBtn(e) {
      this.curPage = 1;
      this.caseDataList = [];
      if (e == this.activeTypeId) {
        this.activeTypeId = "";
      } else {
        this.activeTypeId = e;
      }
      this.getDataList();
    },
    //点击切换风格分类
    areaBtn(e) {
      // this.activeAreaId = e;
      this.curPage = 1;
      this.caseDataList = [];
      if (e == this.activeAreaId) {
        this.activeAreaId = "";
      } else {
        this.activeAreaId = e;
      }
      this.getDataList();
    },

    //输入框事件
    inputValue() {
      console.log(this.keywork);
      // 清除已经设置的定时器
      clearTimeout(this.timeout);

      // 设置新的定时器
      this.timeout = setTimeout(() => {
        this.curPage = 1;
        this.caseDataList = [];
        // 在这里处理输入框的逻辑
        this.getDataList();
      }, 300); // 设置防抖的时间，这里是300毫秒
    },

    //获取列表
    getDataList() {
      this.loading = true;
      this.$axios
        .get(`oe_queryAllCaseMt_.csp`, {
          params: {
            dbName: this.$dbName,
            userId: this.userId,
            curPage: this.curPage,
            style: this.styleActiveId == "-1" ? "" : this.styleActiveId,
            space: this.activeTypeId,
            colour: this.activeAreaId,
            keywork: this.keywork,
          },
        })
        .then((res) => {
          this.loading = false;
          if (res.code == "1") {
            this.caseDataList = res.records;
            this.totalNum = res.count;
            if (res.records.length <= 0 && this.caseDataList.length <= 0) {
              this.dataNull = true;
            } else {
              this.dataNull = false;
            }
            // this.handleData(res.data[0].children);
            // this.typeDataList = res.data[0].children;
            // this.loading = false;
          }
        });
    },

    //获取案例分类
    getCaseTypeData() {
      this.$axios
        .get(`oe_queryCaseTypeMt_.csp`, {
          params: {
            dbName: this.$dbName,
          },
        })
        .then((res) => {
          if (res.code == "1") {
            res.data[0].children.forEach((item) => {
              console.log(item);
              if (item.title == "风格") {
                item.children.unshift({ id: "-1", title: "全部" });
                this.styleData = item.children;
                this.styleActiveId = item.children[0].id;
              } else if (item.title == "户型") {
                this.typeData = item.children;
              } else if (item.title == "面积") {
                this.areaData = item.children;
              }
            });
            // this.caseDataList = res.records;
            // this.totalNum = res.count;
            // this.handleData(res.data[0].children);
            // this.typeDataList = res.data[0].children;
            // this.loading = false;
          }
        });
    },

    //大屏兼容
    keyBnten() {
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape" || e.keyCode === 27) {
          window.top.postMessage("CALL_CSCREEN_GO_HOME", "*");
        }
      });
    },
  },

  //页面生命周期（进入加载）
  created() {
    // this.getDataList();
    // this.getCaseTypeData();
  },
};
</script>

<style  lang="scss" scoped>
.app {
  width: 100%;
  height: 100%;
  padding: 0 0.125rem 0 0.125rem;
  box-sizing: border-box;
  .top_box {
    width: 100%;
    height: 8vh;
    display: flex;
    align-items: center;
    padding: 0.0625rem 0.25rem 0.0625rem 0.375rem;
    box-sizing: border-box;
    margin-top: 0.25rem;

    .back_img {
      width: 0.2375rem;
      height: 0.2375rem;
      cursor: pointer;
    }
    .back_title {
      font-size: 0.2rem;
      color: #434343;
      margin: 0 0.125rem;
    }
    .xian {
      width: 1px;
      height: 0.2062rem;
      background: #434343;
    }
  }

  .content_box {
    width: 100%;
    .yw_text {
      width: 100%;
      display: flex;
      justify-content: center;
      font-size: 0.3125rem;
      text-transform: uppercase;
      font-weight: bold;
      color: #333333;
      margin-top: 0.5rem;
      font-family: "aaa";
    }
    .pp_name_a {
      width: 100%;
      display: flex;
      justify-content: center;
      color: #333333;
      font-size: 0.3125rem;
      margin: 0.1875rem 0;
      font-family: "aaa";
    }
    .pp_box {
      width: 90%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto;
      .pp_boxs {
        width: 4.375rem;
        height: 5rem;
        background: #e4e4e4;
        padding: 0.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        .imga {
          width: 1.375rem;
        }
        .imga1 {
          width: 0.625rem;
        }
        .pp_tu_box {
          width: 3.4375rem;
          height: 2.1875rem;
          margin: 0.4375rem 0;
          .zhong_img {
            width: 100%;
          }
        }
        .pp_mingz {
          font-size: 0.1875rem;
          font-family: "aaa";
        }
      }
    }
  }

  /* 修改垂直滚动条 */
  ::-webkit-scrollbar {
    width: 0.0625rem; /* 修改宽度 */
  }

  /* 修改滚动条轨道背景色 */
  ::-webkit-scrollbar-track {
  }

  /* 修改滚动条滑块颜色 */
  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
  }

  /* 修改滚动条滑块悬停时的颜色 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  /* 修改滚动条滑块移动时的颜色 */
  ::-webkit-scrollbar-thumb:active {
    // background-color: #333;
  }

  /* 修改滚动条滑块的圆角 */
  ::-webkit-scrollbar-thumb {
    border-radius: 0.375rem;
  }

  // 返回
  .back_box {
    // width: 1.875rem;
    // height: 4.5625rem;
    background: rgba(0, 0, 0, 0.44);
    position: absolute;
    right: 0;
    top: 6.25rem;
    color: white;
    border-radius: 6.25rem 0 0 6.25rem;
    display: flex;
    align-items: center;
    padding: 0.125rem 0.25rem 0.125rem 0.125rem;
    z-index: 99999;
    cursor: pointer;
    .back_img1 {
      width: 0.375rem;
      height: 0.375rem;
    }
    .t_name {
      font-size: 0.1563rem;
      margin-left: 0.125rem;
    }
  }

  ::v-deep .el-pagination.is-background .el-pager li {
    margin: 0 0.0625rem !important;
  }

  ::v-deep .el-pagination.is-background .btn-next {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }
  ::v-deep .btn-prev {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }

  ::v-deep .el-pager li {
    font-size: 0.125rem;
    background-color: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }
}
</style>