<template>
  <div class="box">
    <div class="left_box">
      <div class="top_box">
        <img @click="goBack" class="jiantou_img" src="https://cdn.juesedao.cn/mdy/d0ccf550af4b44b99a3130636a26d23f" alt="">
        <div @click="goBack" class="title_box">关于施恩德</div>
        <div class="gang"></div>
      </div>
      <div class="left_c">
        <div class="one_box">
          <div class="a_box">
            <img src="https://cdn.juesedao.cn/mdy/53ab12ce75614972997aac77d3b8fdfe" alt="">
          </div>
          <div class="a_box1">
            <p>SCHENDER</p>
          </div>
        </div>
        <div class="two_box">
          <img :src="selectArr[activeId].content.img" alt="">
        </div>
        <div class="three_box">{{selectArr[activeId].content.cTitle}}</div>
        <div class="wz_text" v-for="(item1,index1) in selectArr[activeId].content.conText" :key="index1">{{item1}}</div>

      </div>
    </div>
    <div class="right_box">
      <div class="tou_box">
        <div class="xian_box">
          <div class="xian"></div>
        </div>
        <div class="xian_text">introduce all</div>
      </div>
      <div class="c_boxa">
        <div class="c_boxs" v-for="(item,index) in selectArr" :key="index" @click="toActive(item,index)">
          <div class="s_left_box">
            <div class="tt1 " :class="activeId == index ? 'active_text' : ''" v-for="(item1,index1) in item.eArr" :key="index1">{{item1}}</div>
            <div class="tt2 " :class="activeId == index ? 'active_text1' : ''">{{item.cName}}</div>
          </div>
          <div class="yuan_box">
            <div class="yuan" style="background:#000;" v-if="activeId == index">
              <img src="https://cdn.juesedao.cn/mdy/70798e788dca449281602e8bb1249aab" alt="">
            </div>
            <div class="yuan" v-else>
              <img src="https://cdn.juesedao.cn/mdy/2e38ee9b7d2f40e28ed98c2fdff9d14a" alt="">
            </div>
          </div>
        </div>
      </div>
    </div>
    <backBtn></backBtn>
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      selectArr: [
        {
          id: "1",
          cName: "关于施恩德",
          eArr: ["SCHENDER GROUP PROFILE"],
          content: {
            img: "https://cdn.juesedao.cn/mdy/397d1146fa9d4c46aed5e19a6784d1cc",
            cTitle: "SCHENDERGROUP PROFILE",
            conText: [
              "SCHENDER GROUP 1979年始创于义大利阿尔贝罗贝洛（Alberobello），总部设在位于义大利中北部的（Emilia -Romagna）艾米利亚罗马涅大区，该地受到得天独厚的气候和地形的共同作用，使得陶瓷产业在全球有著超然的地位。 由卡斯特拉诺 （Castellarano ） 、卡萨尔格兰德 （ Casalgrande ） 和斯坎迪亚诺 （ Scandiano）市以及萨索洛（Sassuolo ），共同构成了义大利非常重要的大型陶瓷工业区，义大利的陶瓷和瓷砖从这里生产和出口到世界各地。",
              "SCHENDER GROUP was founded in Alberobello, Italy in 1979 and is headquartered in the Emilia-Romagna region of north-central Italy, which is blessed with the combination of unique climate and terrain. The ceramic industry has a transcendent position in the world. The cities of Castellarano, Casalgrande and Scandiano, as well as Sassuolo, together form a very important large ceramic industrial zone in Italy, From here Italian ceramics and tiles are produced and exported all over the world.",
              "品牌创始人的Elio是义大利著名的建筑艺术家，他带领团队来到这里，并专注世界重要建筑材料的研发，生产、销售和推广，并成功将 SCHENDER GROUP推上了建筑材料的国际舞台，现时产品畅销全球80多个国家和地区。",
              "Elio, the founder of the brand, is a famous Italian architectural artist. He led the team here to focus on the research and development, production, sales and promotion of the world‘s important building materials, and successfully pushed SCHENDER GROUP onto the international stage of building materials. At present, the products are sold well in more than 80 countries and regions around the world.",
            ],
          },
        },
        {
          id: "2",
          cName: "设计理念",
          eArr: ["Design concept"],
          content: {
            img: "https://cdn.juesedao.cn/mdy/85d89a403b63477e8922e56f653f7ad3",
            cTitle: "Design concept",
            conText: [
              "SCHENDER GROUP广纳贤才，成功组建富于创造能力的设计团队，专注于产品研发并成功打造SCHENDER品牌。 SCHENDER凭借对建材材料的敏锐触角，创新性理念，运用先进的科技，将具有天然石材美感同时兼具超强耐用性的岩板，成功推出将墙壁、地板、工作台面、饰面板、外立面等空间完美设计的解决方案。",
              "SCHENDER GROUP has recruited talents and successfully established a creative design team, focusing on Product Research & Development and successfully building the SCHENDER brand. With its keen reach for building materials, innovative concepts, and advanced technology, SCHENDER has successfully launched solutions that perfectly design walls, floors, countertops, veneer panels, exteriors, and other spaces.",
              "随著科技发展和人们对美的追求，SCHENDER率先形成以320x160cm、300x150cm、270x120cm为核心规格，快速提升产品矩阵在市场竞争优势。 结合工艺要求的不断提升，生产科技的日益完善以及超前的设计触角，产品多次获得义大利陶瓷工业联盟（Confindustria Ceramica）颁发的ADI设计大奖。",
              "With the development of science and technology and people's pursuit of beauty, SCHENDER took the lead in forming 320x160cm, 300x150cm, 270x120cm as the core specifications, rapidly improving the competitive advantage of the product matrix in the market. Combined with the continuous improvement of process requirements, the increasing perfection of production technology and advanced design tentacles, the products have won the ADI Design Award issued by the Confindustria Ceramica for many times.",
            ],
          },
        },
        {
          id: "3",
          cName: "可持续发展",
          eArr: ["sustainable development"],
          content: {
            img: "https://cdn.juesedao.cn/mdy/c64b7018046141cc8c66c8c38b6e73a1",
            cTitle: "sustainable development",
            conText: [
              "SCHENDER GROUP非常重视产业的可持续发展，从产品的设计，材料的筛选，生产系统和销售系统都按照ESC的标准执行。 SCHENDER GROUP与国际设计公司Ares Nitida强强结合，并与整线装备巨头SACMI、西班牙TORRECID、SYSTEM）和MODENA等行业领军企业建立战略合作伙伴关系，产品获得义大利EPD环保产品声明证书、符合ISO国际标准和欧洲标准。 现时岩板产品厚度已全线覆盖厚度范围包含0.3cm、0.45cm、0.6cm及1.2cm。",
              "SCHENDER GROUP attaches great importance to the sustainable development of the industry, from product design, material screening, production system and sales system are implemented in accordance with ESC standards. SCHENDER GROUP combines with the international design company Ares Nitida, and has established strategic partnerships with industry leaders such as the entire line equipment giants SACMI, Spain TORRECID, SYSTEM) and MODENA. The products have obtained the Italian EPD environmental protection product declaration certificate and comply with ISO international standards and European standards. At present, the thickness of rock slab products has been covered in the thickness range of 0.3cm, 0.45cm, 0.6cm and 1.2cm.",
            ],
          },
        },
        {
          id: "4",
          cName: "产品应用范围广泛",
          eArr: ["Wide range of product", "applications"],
          content: {
            img: "https://cdn.juesedao.cn/mdy/2c86b948fed84d30af5a1deb4feb706b",
            cTitle: "Wide range of product applications",
            conText: [
              "SCHENDER GROUP的产品应用范围广泛，广泛满足当代建筑应用需求，产品广泛适用于住宅、商业、饭店和工业等领域。 并成功参与包括西班牙马拉加机场（Malaga Airport）、瑞士思迪尼曼饭店（Steeniman Hotel）等知名工程项目。",
              "SCHENDER GROUP's products are widely used in a wide range of applications to meet the needs of contemporary architectural applications. The products are widely used in residential, commercial, hotel and industrial fields. And successfully participated in well-known engineering projects including Malaga Airport in Spain and Steeniman Hotel in Switzerland.",
              "在与国际设计团队的强强结合下，SCHENDER在岩板产品设计、跨界的岩板家具设计、大型商业项目产品应用设计方面不断创新，为合作伙伴提供全岩整装交付服务体系，全面满足商业，居住，旅游，工业，公共项目等不同业态的个性化定制需求",
              "With the strong combination of the international design team, SCHENDER continuously innovates in the design of rock slab products, cross-border rock slab furniture design, and large-scale commercial project product application design, providing partners with a full-rock integrated delivery service system to fully meet the personalized customization needs of different business formats such as business, residence, tourism, industry, and public projects",
            ],
          },
        },
      ],
      activeId: "0",
    };
  },
  components: {
    backBtn,
  },
  methods: {
    toActive(item, id) {
      this.activeId = id;
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  padding: 0.5rem 0.5rem 0 0.5rem;
  box-sizing: border-box;
  display: flex;
  .left_box {
    width: 11.25rem;
    // height: 3.75rem;
    border-right: 1px solid #e5e5e5;
  }
  .top_box {
    width: 100%;
    display: flex;
    align-items: center;
    .jiantou_img {
      width: 0.2188rem;
      height: 0.2188rem;
      cursor: pointer;
    }
    .title_box {
      font-size: 0.2rem;
      color: #434343;
      margin: 0 0.1125rem;
      font-weight: 400;
      cursor: pointer;
    }
    .gang {
      width: 1px;
      height: 0.1875rem;
      background: #434343;
    }
  }
  .left_c {
    width: 100%;
    height: 100%;
    padding: 0.375rem 0.75rem 0 0.625rem;
    box-sizing: border-box;
    .one_box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.125rem;
      .a_box {
        width: 0.575rem;
        // height: 0.275rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .a_box1 {
        font-size: 0.0875rem;
        color: #8b8d8b;
        text-transform: uppercase;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        font-family: "aaa";
      }
    }
    .two_box {
      width: 100%;
      //   height: 100%;
      margin-top: 0.0625rem;
      img {
        width: 100%;
        // height: 2.5rem;
      }
    }
    .three_box {
      font-size: 0.2938rem;
      text-transform: uppercase;
      font-weight: bold;
      margin-top: 0.1375rem;
      font-family: "aaa";
    }
    .wz_text {
      font-size: 0.125rem;
      color: #272727;
      margin-top: 0.25rem;
      font-family: "aaa";
    }
  }
  .right_box {
    width: 5rem;
    padding: 0.5rem 0 0.5rem 0.5rem;
    box-sizing: border-box;
    .tou_box {
      width: 100%;
      margin-top: 0.5313rem;
      margin-bottom: 0.0625rem;
      .xian_box {
        width: 100%;
        height: 2px;
        background: #e8e8e8;
        .xian {
          width: 0.4375rem;
          height: 2px;
          background: black;
        }
      }
      .xian_text {
        font-size: 0.0938rem;
        text-transform: uppercase;
        font-weight: 700;
        margin-top: 0.0625rem;
      }
    }
    .c_boxa {
      width: 100%;
      .c_boxs {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 0.625rem;
        cursor: pointer;
        .s_left_box {
          .tt1 {
            font-size: 0.1625rem;
            color: #6c6c6c;
            text-transform: uppercase;
            font-family: "aaa";
          }
          .tt2 {
            font-size: 0.1375rem;
            color: #6c6c6c;
            font-weight: 400;
            margin-top: 0.0625rem;
          }
          .active_text {
            font-size: 0.1812rem;
            font-weight: 700;
            color: black;
            text-decoration: underline;
          }
          .active_text1 {
            font-size: 0.1375rem;
            color: black;
            font-family: "aaa";

            text-decoration: underline;
          }
        }
        .yuan_box {
          width: 0.5rem;
          height: 100%;
          .yuan {
            width: 0.3563rem;
            height: 0.3563rem;
            // background: black;
            border-radius: 3.75rem;
            border: 1px solid #6c6c6c;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              width: 0.15rem;
              height: 0.15rem;
            }
          }
        }
      }
    }
  }
}
</style>