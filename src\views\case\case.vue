<template>
  <div class="app" v-loading="loading">
    <backBtn></backBtn>
    <div class="top_box">
      <img @click="goBack" class="back_img" src="../../assets/images/back.png" alt="">
      <p class="back_title">案例列表</p>
      <p class="xian"></p>
      <!-- <p class="back_title" @click="open">清除缓存</p> -->

    </div>
    <keep-alive>
      <div class="content_box">
        <!-- <div class="left_box">
          <ul class="ul">
            <li class="li" :class="item.id == styleActiveId ? 'active_left' : ''" v-for="(item,index) in styleData" :key="index" @click="styleBtn(item.id)">{{item.title}}</li>
          </ul>
        </div> -->
        <div class="right_box">
          <div class="right_top">
            <div class="top_left">
              <ul class="ul">
                <li class="li" :class="item.id == spaceAreaId ? 'active_type' : ''" v-for="(item,index) in spaceData" :key="index" @click="typeBtn(item.id)">{{item.title}}</li>
              </ul>
              <!-- <ul class="ul">
                <li class="li" :class="item.id == activeAreaId ? 'active_type' : ''" v-for="(item,index) in areaData" :key="index" @click="areaBtn(item.id)">{{item.title}}</li>
              </ul> -->
            </div>
            <div class="top_right">
              <div class="input_box">
                <input type="text" v-model="keywork" @input="inputValue" class="input" placeholder="搜索产品信息">
                <img class="select_img" src="../../assets/images/select.png" alt="">
              </div>
            </div>
          </div>

          <div class="product_list" v-show="!dataNull">
            <div class="pro_boxs" v-for="(item,index) in caseDataList" :key="index" @click="toDetails(item.id)">
              <div class="img_box">
                <img class="cp_img" :src="item.zhuye" alt="">
              </div>
              <div class="cp_name">{{item.name}}</div>
            </div>
          </div>

          <div class="product_list1" v-show="dataNull">
            <el-empty description="暂无案例"></el-empty>
          </div>

          <div class="fenye_box">
            <el-pagination background layout="prev, pager, next" @current-change="changePage" hide-on-single-page :page-size="12" :total="totalNum">
            </el-pagination>
          </div>

        </div>
      </div>
    </keep-alive>
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      userId: "1",
      curPage: "1",
      caseDataList: [], //案例数据
      totalNum: 0,
      styleActiveId: "", //风格选中id
      activeTypeId: "", //户型选中id
      activeAreaId: "", //户型选中id
      spaceAreaId: "", //户型选中id
      styleData: [], //风格
      typeData: [], //户型
      areaData: [], //面积
      spaceData: [], //空间
      loading: false,
      keywork: "",
      timeout: null,
      dataNull: false,
    };
  },
  components: {
    backBtn,
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },

    changePage(e) {
      this.curPage = e;
      this.dataNull = false;
      this.caseDataList = [];
      this.getDataList();
    },

    //跳转详情
    toDetails(id) {
      this.$router.push({ path: "/caseDetails", query: { id: id } });
      // const url = "https://mi.medodt.com/UrlToHtml?url=" + url1; // 替换为你的公众号链接
      // window.location.href = url;
      // this.$router.push({
      //   path: "/viewWeb",
      //   query: { url: url1 },
      // });
    },

    //点击切换风格分类
    styleBtn(e) {
      this.styleActiveId = e;
      ((this.caseDataList = []), (this.curPage = 1)), this.getDataList();
    },
    //点击切换风格分类
    typeBtn(e) {
      this.curPage = 1;
      this.caseDataList = [];
      if (e == this.spaceAreaId) {
        this.spaceAreaId = "";
      } else {
        this.spaceAreaId = e;
      }
      this.getDataList();
    },
    //点击切换风格分类
    areaBtn(e) {
      // this.activeAreaId = e;
      this.curPage = 1;
      this.caseDataList = [];
      if (e == this.activeAreaId) {
        this.activeAreaId = "";
      } else {
        this.activeAreaId = e;
      }
      this.getDataList();
    },

    //输入框事件
    inputValue() {
      console.log(this.keywork);
      // 清除已经设置的定时器
      clearTimeout(this.timeout);

      // 设置新的定时器
      this.timeout = setTimeout(() => {
        this.curPage = 1;
        this.caseDataList = [];
        // 在这里处理输入框的逻辑
        this.getDataList();
      }, 300); // 设置防抖的时间，这里是300毫秒
    },

    //获取列表
    getDataList() {
      this.loading = true;
      this.$axios
        .get(`oe_queryAllCaseSed_.csp`, {
          params: {
            dbName: "schender",
            userId: this.userId,
            curPage: this.curPage,
            style: this.styleActiveId == "-1" ? "" : this.styleActiveId,
            space: this.spaceAreaId == "-1" ? "" : this.spaceAreaId,
            colour: this.activeAreaId,
            keyword: this.keywork,
          },
        })
        .then((res) => {
          this.loading = false;
          if (res.code == "1") {
            console.log(res.total);
            this.caseDataList = res.records;
            this.totalNum = res.total;
            if (res.records.length <= 0 && this.caseDataList.length <= 0) {
              this.dataNull = true;
            } else {
              this.dataNull = false;
            }
            // this.handleData(res.data[0].children);
            // this.typeDataList = res.data[0].children;
            // this.loading = false;
          }
        });
    },

    open() {
      this.$confirm("此操作将退出登录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        // type: 'warning'
      })
        .then(() => {
          //清除某一项
          // Cookies.remove('shopInfo') // value
          localStorage.removeItem("shopInfo1");
          this.dialogVisible = false; //登录框状态
          this.$message({
            type: "success",
            message: "退出成功",
          });
          setTimeout(() => {
            this.$router.push({ path: "/" });
          }, 1000);
        })
        .catch(() => {});
    },

    //获取案例分类
    getCaseTypeData() {
      this.$axios
        .get(`oe_queryCaseTypeSed_.csp`, {
          params: {
            dbName: "schender",
          },
        })
        .then((res) => {
          if (res.code == "1") {
            res.data[0].children.forEach((item) => {
              console.log(item);
              if (item.title == "空间") {
                item.children.unshift({ id: "-1", title: "全部" });
                this.spaceData = item.children;
                this.spaceAreaId = item.children[0].id;
              }
              // else if (item.title == "户型") {
              //   this.typeData = item.children;
              // } else if (item.title == "面积") {
              //   this.areaData = item.children;
              // }
            });
            // this.caseDataList = res.records;
            // this.totalNum = res.count;
            // this.handleData(res.data[0].children);
            // this.typeDataList = res.data[0].children;
            // this.loading = false;
          }
        });
    },

    //大屏兼容
    keyBnten() {
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape" || e.keyCode === 27) {
          window.top.postMessage("CALL_CSCREEN_GO_HOME", "*");
        }
      });
    },
  },

  //页面生命周期（进入加载）
  created() {
    this.getDataList();
    this.getCaseTypeData();
  },
};
</script>

<style  lang="scss" scoped>
.app {
  width: 100%;
  height: 100%;
  padding: 0 0.125rem 0 0.125rem;
  box-sizing: border-box;
  .top_box {
    width: 100%;
    height: 7vh;
    display: flex;
    align-items: center;
    padding: 0.0625rem 0.25rem 0.0625rem 0.375rem;
    box-sizing: border-box;
    margin-top: 0.125rem;
    .back_img {
      width: 0.2375rem;
      height: 0.2375rem;
      cursor: pointer;
    }
    .back_title {
      font-size: 0.2rem;
      color: #434343;
      margin: 0 0.125rem;
    }
    .xian {
      width: 1px;
      height: 0.2062rem;
      background: #434343;
    }
  }

  .content_box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .left_box {
      width: 1.1875rem;
      height: 89vh;
      background: #f6f5f2;
      border-radius: 0.3125rem;
      padding: 0 0.0625rem 0.0625rem;
      box-sizing: border-box;
      .ul {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
        overflow-y: auto;
        .li {
          list-style: none;
          font-size: 0.1563rem;
          color: #838383;
          height: 0.1875rem;
          cursor: pointer;
          padding: 0.25rem 0;
          margin-bottom: 0.2188rem;
        }
        .active_left {
          font-size: 0.1875rem;
          font-weight: bold;
          color: #000000;
          position: relative;
          ::after {
            content: "";
            width: 2.25rem;
            height: 0.25rem;
            border-radius: 0.25rem;
            background: #3782f4;
            position: absolute;
            left: 50%;
            bottom: 0.625rem;
            transform: translateX(-50%);
          }
        }
      }
    }
    .right_box {
      width: 96%;
      height: 89vh;
      margin: 0 auto;
      // background: #eef4fb;
      .right_top {
        width: 100%;
        height: 0.75rem;
        background: #f6f5f2;
        border-radius: 0.25rem;
        display: flex;
        align-items: center;
        padding: 0 0.125rem 0 0.125rem;
        box-sizing: border-box;
        .top_left {
          width: 82%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          .ul {
            display: flex;
            align-items: center;
            padding: 0.0625rem 0.125rem;
            overflow-x: auto;
            box-sizing: border-box;
            .li {
              list-style: none;
              padding-right: 0.1563rem;
              margin-right: 0.0688rem;
              cursor: pointer;
              color: #8a8a8a;
              font-size: 0.125rem;
            }
            .active_type {
              color: black;
              font-weight: bold;
            }
            ::-webkit-scrollbar:vertical {
              width: 0.375rem;
            }
          }
        }
        .top_right {
          width: 18%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .input_box {
            width: 80%;
            height: 0.4375rem;
            position: relative;
            .input {
              width: 100%;
              height: 100%;
              background: #ffffff;
              border: none;
              border-radius: 2.875rem;
              font-size: 0.125rem;
              padding: 0 0.125rem 0 0.125rem;
              box-sizing: border-box;
            }
            .select_img {
              width: 0.1875rem;
              height: 0.1875rem;
              position: absolute;
              top: 50%;
              right: 0.125rem;
              transform: translateY(-50%);
              cursor: pointer;
            }
          }
        }
      }

      .product_list {
        width: 100%;
        height: 6.25rem;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 0.1875rem;
        margin-top: 0.1875rem;
        overflow-y: auto;
        .pro_boxs {
          width: 100%;
          cursor: pointer;
          .img_box {
            width: 100%;
            height: 2.125rem;
            .cp_img {
              width: 100%;
              height: 100%;
            }
          }
          .cp_name {
            font-size: 0.1437rem;
            font-weight: bold;
            margin: 0.0938rem 0 0 0;
            color: #202020;
            font-family: "aaa";
          }
          .cp_gge {
            font-size: 0.75rem;
            color: #202020;
          }
        }
      }

      .product_list1 {
        width: 100%;
        height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .fenye_box {
        width: 100%;
        // height: 3.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 0.25rem;
      }
    }
  }

  /* 修改垂直滚动条 */
  ::-webkit-scrollbar {
    width: 0.0625rem; /* 修改宽度 */
  }

  /* 修改滚动条轨道背景色 */
  ::-webkit-scrollbar-track {
  }

  /* 修改滚动条滑块颜色 */
  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
  }

  /* 修改滚动条滑块悬停时的颜色 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  /* 修改滚动条滑块移动时的颜色 */
  ::-webkit-scrollbar-thumb:active {
    // background-color: #333;
  }

  /* 修改滚动条滑块的圆角 */
  ::-webkit-scrollbar-thumb {
    border-radius: 0.375rem;
  }

  // 返回
  .back_box {
    // width: 1.875rem;
    // height: 4.5625rem;
    background: rgba(0, 0, 0, 0.44);
    position: absolute;
    right: 0;
    top: 6.25rem;
    color: white;
    border-radius: 6.25rem 0 0 6.25rem;
    display: flex;
    align-items: center;
    padding: 0.125rem 0.25rem 0.125rem 0.125rem;
    z-index: 99999;
    cursor: pointer;
    .back_img1 {
      width: 0.375rem;
      height: 0.375rem;
    }
    .t_name {
      font-size: 0.1563rem;
      margin-left: 0.125rem;
    }
  }

  ::v-deep .el-pagination.is-background .el-pager li {
    margin: 0 0.0625rem !important;
  }

  ::v-deep .el-pagination.is-background .btn-next {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }
  ::v-deep .btn-prev {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }

  ::v-deep .el-pager li {
    font-size: 0.125rem;
    background-color: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }
  ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: black;
  }
}
</style>