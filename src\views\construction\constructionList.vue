<template>
  <div class="app" v-loading="loading">
    <backBtn></backBtn>
    <Map v-show="activeId == '1'"></Map>
    <!-- <backBtn></backBtn> -->
    <div class="top_box" v-show="activeId == 2">
      <div class="left_box">
        <div class="title_box">
          <img @click="toBack" class="back_img" src="../../assets/images/back.png" alt="">
          <p class="top_title">全部工地列表</p>
          <p class="xian"></p>
        </div>
        <div class="shaix_box">
          <div class="sx">
            <!-- <span class="sp1">工地城市</span>
            <img class="img2" src="../../assets/images/sx.png" alt=""> -->
            <!-- <el-select v-model="selectedCountry" placeholder="工地城市">
              <el-option v-for="country in countries" :key="country.value" :label="country.label" :value="country.value">
              </el-option>
            </el-select> -->
            <el-cascader style="width: 100%" :options="options" clearable v-model="selectedOptions" @change="addressChoose"></el-cascader>
          </div>
          <div class="sx">
            <!-- <el-select v-model="selectedCountry" placeholder="工地区域">
              <el-option v-for="country in countries" :key="country.value" :label="country.label" :value="country.value">
              </el-option>
            </el-select> -->
            <!-- <span class="sp1">工地区域</span>
            <img class="img2" src="../../assets/images/sx.png" alt=""> -->
          </div>

          <div class="sx">
            <el-select v-model="schedule" @change="shigongChang" clearable placeholder="施工阶段">
              <el-option size="large" v-for="country in shigongStatus" :key="country.value" :label="country.label" :value="country.value">
              </el-option>
            </el-select>
            <!-- <span class="sp1">施工阶段</span>
            <img class="img2" src="../../assets/images/sx.png" alt=""> -->
          </div>
        </div>
      </div>

    </div>
    <div class="right_box">
      <div class="select_box">
        <p @click="activeBtn(1)" class="box_s" :class="activeId == '1' ? 'active_box' : ''">
          <img v-if="activeId != '1'" class="yuan_imga" src="https://cdn.juesedao.cn/mdy/d76c4db174c047af9594bee52b0c5e26" alt="">
          <span>按列表查看</span>
        </p>
        <p @click="activeBtn(2)" class="box_s" :class="activeId == '2' ? 'active_box' : ''">
          <span>按列表查看</span>
          <img v-if="activeId != '2'" class="yuan_imga1" src="https://cdn.juesedao.cn/mdy/d76c4db174c047af9594bee52b0c5e26" alt="">
        </p>
      </div>
    </div>

    <div class="con_box" v-if="!isNull && activeId == '2'">
      <div class="box_s1" v-for="(item,index) in gongdiList" :key="index" @click="toDetails(item.id)">
        <div class="st_img">
          <!-- <img class="img_aa" :src="item.coverPic" alt=""> -->
          <el-image class="img_aa" :src="item.coverPic" fit="cover"></el-image>
        </div>
        <div class="name1">{{item.city ? item.city + '|' : ''}}{{item.community}}</div>
        <div class="name2">{{item.houseType ? item.houseType + ' |' : ''}} {{item.houseArea ? item.houseArea + '平方m2 丨' : ''}} {{item.statusName}}</div>
      </div>

    </div>
    <div v-if="isNull && activeId == '2'" style="width: 90%; min-height: 79vh; display: flex; align-items: center; justify-content: center;">
      <el-empty description="暂无工地" image-size="200"></el-empty>
    </div>

    <div class="fenye_box" v-show="activeId == '2'">
      <!-- <el-pagination background hide-on-single-page @current-change="currentChange" layout="prev, pager, next" :total="totalNum">
      </el-pagination> -->

      <el-pagination background layout="prev, pager, next" @current-change="currentChange" hide-on-single-page :total="totalNum">
      </el-pagination>
    </div>

    <!-- 返回按钮 -->
    <!-- <div class="back_box" @click="toBack">
      <img class="back_img1" src="../../assets/images/back_icon.png" alt="">
      <p class="t_name">返回</p>
    </div> -->

  </div>
</template>

<script>
import Map from "../../components/amap.vue";
import backBtn from "../../components/backBtn.vue";
import {
  regionData, // 省市区三级联动数据
  codeToText, // 这是6.1.0版本 5.0.2稳定版是CodeToText 小写的注意！
  pcaTextArr, // 省市区联动数据，纯汉字
  TextToCode, // 5.0.2可以使用 TextToCode['北京']
} from "element-china-area-data";
export default {
  data() {
    return {
      activeId: "2",
      statusCountry: "",
      gongdiList: [],
      totalNum: "0",
      province: "",
      curPage: "1",
      isNull: false,
      city: "",
      area: "",
      orderStatus: "",
      options: pcaTextArr, // 省市区级联数据
      selectedOptions: [], // 选择的地区
      selectedOptions: "",
      keyword: "",
      schedule: "",
      loading: false,
      selctQuyuArr: [{ value: "洗手间", label: "洗手间" }],
      stausArr: [
        { value: "量好尺寸", label: "量好尺寸" },
        { value: "出好设计", label: "出好设计" },
        { value: "跟好现场", label: "跟好现场" },
        { value: "做好美缝", label: "做好美缝" },
        { value: "搞好清洁", label: "搞好清洁" },
        { value: "拍好美片", label: "拍好美片" },
      ],
      shigongStatus: [
        { value: "1", label: "门店接单" },
        { value: "2", label: "总部确认" },
        { value: "3", label: "施工中" },
        { value: "4", label: "施工完成" },
        { value: "5", label: "项目验收" },
        { value: "6", label: "终身质保" },
      ],
    };
  },
  // components: {
  //   backBtn,
  // },
  components: {
    Map,
    backBtn,
    // backBtn,
  },
  methods: {
    toBack() {
      this.$router.go(-1);
    },
    activeBtn(e) {
      this.activeId = e;
    },

    //筛选进度
    activeStatus(e) {
      console.log(e);
    },

    //施工进度筛选
    shigongChang(e) {
      this.schedule = e;
      this.curPage = 1;
      this.gongdiList = [];
      this.getGongdiList();
    },

    //筛选区域
    addressChoose(value) {
      console.log(value);
      this.province = value[1];
      this.curPage = 1;
      this.gongdiList = [];
      this.getGongdiList();
    },

    //跳转详情
    toDetails(id) {
      this.$router.push({ path: "/constructionDetails", query: { id: id } });
    },

    //点击分页
    currentChange(e) {
      console.log(e);
      this.curPage = e;
      this.gongdiList = [];
      this.getGongdiList();
    },

    //获取工地列表
    getGongdiList() {
      let { curPage, city, area, orderStatus, keyword, schedule, province } =
        this;
      this.isNull = false;
      this.loading = true;
      this.$axios
        .get(`oe_workOnline_.csp`, {
          params: {
            dbName: "seddelivery",
            curPage,
            province,
            area,
            keyword,
            schedule,
          },
        })
        .then((res) => {
          this.loading = false;
          console.log(res);
          if (res.code == "1") {
            if (res.list.length > 0) {
              this.isNull = false;
            } else {
              this.isNull = true;
            }
            res.list.forEach((item) => {
              this.shigongStatus.forEach((item1) => {
                if (item.schedule == item1.value) {
                  item.statusName = item1.label;
                }
              });
            });

            this.gongdiList = res.list;
            this.totalNum = res.totalNum;
          } else {
            this.isNull = true;
          }
        });
    },
  },
  created() {
    this.activeId = this.$route.query.activeId;
    this.getGongdiList();
  },
};
</script>

<style lang="scss" scoped>
.app {
  width: 100%;
  min-height: 100vh;
  // background: linear-gradient(180deg, #fff 0%, #e4edfc 55%, #f0f5fb 100%);
  .top_box {
    width: 91%;
    // height: 5.625rem;
    display: flex;
    align-content: flex-end;
    justify-content: space-between;
    margin: 0 auto;
    padding-top: 0.125rem;
    // position: relative;

    .left_box {
      width: 70%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;

      .title_box {
        display: flex;
        align-items: center;
        padding-left: 0.0625rem;
        box-sizing: border-box;
        height: 6vh;
        .back_img {
          width: 0.2375rem;
          height: 0.2375rem;
          cursor: pointer;
        }
        .top_title {
          font-size: 0.2rem;
          color: #434343;
          margin: 0 0.125rem;
        }
        .xian {
          width: 1px;
          height: 0.2062rem;
          background: #434343;
        }
      }
      .shaix_box {
        width: 100%;
        display: flex;
        align-items: center;
        margin-top: 0.0313rem;
        .sx {
          // height: 0.5rem;
          display: inline-block;
          border-radius: 1.875rem;
          // padding: 0 0.125rem;
          margin-left: 0.0625rem;
          // line-height: 0.25rem;
          cursor: pointer;
          .sp1 {
            font-size: 0.125rem;
            color: white;
          }
          .img2 {
            width: 0.0625rem;
            height: 0.0625rem;
            margin-left: 0.125rem;
          }
        }
      }
    }
  }

  .right_box {
    width: 30%;
    //   height: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    position: absolute;
    right: 5%;
    top: 0.25rem;
    z-index: 99;
    .select_box {
      width: 3.5rem;
      height: 0.6875rem;
      border-radius: 1.25rem;
      // border: 0.0063rem solid #d8dde4;
      box-sizing: border-box;
      box-shadow: 0px 0px 0.3438rem 0px rgba(0, 0, 0, 0.34);
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: white;
      padding: 0 0.0625rem;
      box-sizing: border-box;
      .box_s {
        width: 50%;
        height: 100%;
        font-size: 0.2rem;
        color: #a8a8a8;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      .active_box {
        background: #000;
        height: 87%;
        color: white;
        border-radius: 1.25rem;
        font-size: 0.225rem;
        font-weight: bold;
      }
    }
  }

  .con_box {
    width: 90%;
    max-height: 78vh;
    margin: 0.125rem auto 0.125rem;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    overflow-y: auto;
    gap: 0.125rem;
    .box_s1 {
      width: 100%;
      cursor: pointer;
      //   height: 16.875rem;
      .st_img {
        width: 100%;
        // height: 100%;
        .img_aa {
          width: 100%;
          height: 33vh;
        }
      }
      .name1 {
        font-size: 0.1875rem;
        font-weight: bold;
        margin: 0.0625rem 0;
      }
      .name2 {
        font-size: 0.125rem;
        color: #212121;
      }
    }
  }
  .fenye_box {
    width: 100%;
    // height: 3.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  /* 自定义滚动条整体样式 */
  ::-webkit-scrollbar {
    width: 5px !important; /* 设置滚动条的宽度 */
    height: 0.375rem;
  }

  /* 自定义滚动条滑块样式 */
  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.27); /* 设置滑块的颜色 */
    border-radius: 0.3125rem;
  }

  /* 自定义滚动条轨道样式 */
  ::-webkit-scrollbar-track {
    background-color: #fff; /* 设置轨道的颜色 */
    border-radius: 0.3125rem;
  }

  .el-pagination .btn-prev,
  .el-pagination .btn-next {
    background-color: #f5f5f5; /* 举例：更改分页按钮的背景颜色 */
  }

  ::v-deep .el-input.is-focus .el-input__inner {
    border-color: #000 !important;
  }

  ::v-deep .el-pagination.is-background .el-pager li {
    margin: 0 0.0625rem !important;
  }

  ::v-deep .el-pagination.is-background .btn-next {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }
  ::v-deep .btn-prev {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }

  ::v-deep .el-pager li {
    font-size: 0.125rem;
    background-color: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }

  ::v-deep .el-input__icon {
    font-size: 0.1563rem;
    width: 0.25rem;
    line-height: 0.3125rem;
    // height: 0.2813rem;
  }

  ::v-deep .el-input__inner {
    height: 0.3125rem !important;
    line-height: 0.25rem !important;
    overflow: hidden;
  }
  ::v-deep .el-input {
    font-size: 0.125rem;
  }

  ::v-deep .el-select-dropdown__item {
    font-size: 0.0625rem !important;
  }

  ::v-deep .el-empty__description p {
    font-size: 0.125rem !important;
  }
  .yuan_imga {
    width: 0.2813rem;
    height: 0.2813rem;
    margin-right: 0.0625rem;
  }
  .yuan_imga1 {
    width: 0.2813rem;
    height: 0.2813rem;
    margin-left: 0.0625rem;
  }
  ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: black;
  }
  // 返回
  // .back_box {
  //   // width: 1.875rem;
  //   // height: 4.5625rem;
  //   background: rgba(0, 0, 0, 0.44);
  //   position: absolute;
  //   right: 0;
  //   top: 7.5rem;
  //   color: white;
  //   border-radius: 6.25rem 0 0 6.25rem;
  //   display: flex;
  //   align-items: center;
  //   padding: 0.125rem 0.25rem 0.125rem 0.125rem;
  //   z-index: 99999;
  //   cursor: pointer;
  //   .back_img1 {
  //     width: 0.375rem;
  //     height: 0.375rem;
  //   }
  //   .t_name {
  //     font-size: 0.1563rem;
  //     margin-left: 0.125rem;
  //   }
  // }
}
</style>