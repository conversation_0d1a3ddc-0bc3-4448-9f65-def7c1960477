<template>
  <div class="box" v-loading="loading">
    <backBtn></backBtn>
    <div class="top_box">
      <img @click="toBack" class="back_img" :src="topData.backImg" alt="">

      <p class="back_title">{{topData.title}}</p>
      <p class="xian"></p>
    </div>
    <div class="introduce_box">
      <div class="left_logo_box1"></div>
      <div class="right_logo_name">
        <span class="sp1">{{detailsData.brandName}}</span>
        <span>{{detailsData.brandPlace}}</span>
      </div>
    </div>
    <div class="introduce_box1">
      <div class="left_logo_box">
        <!-- <img class="logo_img" :src="detailsData.logo" alt=""> -->
        <el-image class="logo_img" :src="detailsData.logo" fit="scale-down"></el-image>
      </div>
      <div class="about_box">{{detailsData.about}}</div>
      <div class="gw_box" @click="toGwqx">
        <img class="gw_img" src="https://cdn.juesedao.cn/mdy/a2bf50c0aa8b4cb58afe8dd73e0fe697" alt="">
      </div>
    </div>
    <div class="type_box">
      <div class="type_left">
        <p @click="typeIdBtn(item.id)" class="p1" :class="item.id == typeId ? 'p1s' : ''" v-for="(item,index) in typeList" :key="index">{{item.name}}</p>
      </div>
      <div class="type_right"></div>
    </div>

    <div class="pr_box" v-if="!isNull">
      <div class="p_boxs" v-for="(item,index) in typeProdectList" :key="index" @click="toBrandDetails(item.id)">
        <div class="img_boxb">
          <!-- <img class="pp_img" :src="item.productUrl" alt=""> -->
          <el-image class="pp_img" :src="item.productUrl" fit="contain"></el-image>

        </div>
        <div class="pp_name">{{item.name}}</div>
        <div class="pp_eName">{{item.brandName}}</div>
      </div>
    </div>
    <div class="pr_box1" v-if="isNull">
      <el-empty description="暂无数据"></el-empty>
    </div>

    <div class="fenye_box">
      <el-pagination background hide-on-single-page @current-change="changePage" layout="prev, pager, next" :total="totalNum">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      topData: {
        title: "品牌详情",
        backImg: "https://cdn.juesedao.cn/mdy/e42a3bc1b0fc4dc2a6f56090e1022195",
      },
      brandId: "",
      curPage: "1",
      detailsData: "",
      typeList: [],
      activeId: "",
      category1: "",
      typeProdectList: [],
      totalNum: 0,
      typeId: "",
      loading: false,
      isNull: false,
    };
  },
  components: {
    backBtn,
  },
  methods: {
    toBack() {
      this.$router.go(-1);
    },

    //跳转欧翠官网
    toGwqx() {
      this.$router.push({ path: "/brand" });
    },

    typeIdBtn(e) {
      this.typeId = e;
      this.activeId = e;
      this.typeProdectList = [];
      this.curPage = 1;
      this.totalNum = 0;
      this.isNull = false;
      this.getDetailsList();
    },

    //获取详情
    getDataList() {
      let that = this;
      this.$axios
        .get(`oe_getOucuiData_.csp`, {
          params: {
            code: "getBrandById",
            id: this.brandId,
          },
        })
        .then((res) => {
          this.detailsData = res.result;
          this.activeId = this.brandId;
          if (res.productType && res.productType.length > 0) {
            res.productType.unshift({ id: "-1", name: "全部" });
            this.typeId = res.productType[0].id;
          }
          this.typeList = res.productType;
          this.getDetailsList();
        });
    },

    //获取产品列表
    getDetailsList() {
      let that = this;
      that.loading = true;
      this.$axios
        .get(`oe_getOucuiData_.csp`, {
          params: {
            code: "brandGoodsListEn",
            curPage: this.curPage,
            category1: this.typeId == "-1" ? "" : this.typeId,
            category2: "",
            name: "",
            brandId: this.brandId,
          },
        })
        .then((res) => {
          that.loading = false;
          if (res.code == "1") {
            if (res.result.length <= 0) {
              that.isNull = true;
            }
            this.typeProdectList = res.result;
            this.totalNum = res.totalNum;
          } else {
            that.isNull = true;
          }
        });
    },

    changePage(e) {
      this.curPage = e;
      this.getDetailsList();
    },

    //跳转品牌产品详情
    toBrandDetails(e) {
      this.$router.push({ path: "/productOuDetails", query: { id: e } });
    },
  },
  created() {
    // console.log(this.$route.query.id);
    this.brandId = this.$route.query.id;
    this.getDataList();
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  padding: 0.3438rem 0.375rem 0 0.375rem;
  box-sizing: border-box;
  .top_box {
    width: 100%;
    display: flex;
    align-items: center;
    .back_img {
      width: 0.2375rem;
      height: 0.2375rem;
      cursor: pointer;
    }
    .back_title {
      font-size: 0.2rem;
      color: #434343;
      margin: 0 0.125rem;
    }
    .xian {
      width: 1px;
      height: 0.2062rem;
      background: #434343;
    }
  }
  .introduce_box {
    width: 100%;
    display: flex;
    margin-top: 0.25rem;
    .left_logo_box1 {
      width: 1.5rem;
      margin: 0 0.1625rem;
    }
    .right_logo_name {
      font-size: 0.1625rem;
      color: black;
      font-weight: 400;
      .sp1 {
        margin: 0 0.1625rem 0 0;
      }
    }
  }
  .introduce_box1 {
    width: 100%;
    display: flex;
    margin-top: 0.125rem;
    border-bottom: 1px solid #878787;
    padding-bottom: 0.25rem;

    .left_logo_box {
      width: 1.5rem;
      height: 0.625rem;
      margin: 0 0.1625rem;
      .logo_img {
        width: 100%;
        height: 100%;
      }
    }
    .about_box {
      width: 12.5rem;
      font-size: 0.1187rem;
      //   height: 100%;
    }
  }
  .gw_box {
    width: 0.5rem;
    height: 0.75rem;
    cursor: pointer;
    margin-left: 0.25rem;
    .gw_img {
      width: 100%;
      height: 100%;
    }
  }
  .type_box {
    width: 100%;
    height: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .type_left {
      display: flex;
      align-items: center;
      font-size: 0.1563rem;
      font-weight: 400;
      color: #8c8c8c;
      .p1 {
        margin-right: 0.5rem;
        cursor: pointer;
      }
      .p1s {
        color: #000;
      }
    }
  }
  .pr_box {
    width: 100%;
    min-height: 5.3125rem;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.125rem;
    .p_boxs {
      width: 3.75rem;
      height: 2.5rem;
      margin-top: 0.1rem;
      .img_boxb {
        width: 100%;
        height: 2.125rem;
        background: #f6f6f6;
        display: flex;
        align-items: center;
        justify-content: center;
        .pp_img {
          width: 80%;
          height: 80%;
        }
      }
      .pp_name {
        font-size: 0.15rem;
        color: #1c1c1c;
        font-weight: 400;
        margin-top: 0.1rem;
      }
      .pp_eName {
        font-size: 0.0938rem;
        color: #202020;
        font-weight: 400;
      }
    }
  }
  .pr_box1 {
    width: 100%;
    min-height: 5.3125rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .fenye_box {
    width: 100%;
    // height: 3.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 0.25rem;
  }
  ::v-deep .el-pagination.is-background .el-pager li {
    margin: 0 0.0625rem !important;
  }

  ::v-deep .el-pagination.is-background .btn-next {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }
  ::v-deep .btn-prev {
    margin: 0 0.0625rem;
    background: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }

  ::v-deep .el-pager li {
    font-size: 0.125rem;
    background-color: transparent;
    border: 0.0063rem solid #ccc;
    box-sizing: border-box;
    font-weight: none;
    min-width: 0.3125rem;
    border-radius: 0.0313rem;
    height: 0.3125rem;
    line-height: 0.3125rem;
  }

  ::v-deep .el-image-viewer__mask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    background: #000;
  }

  /* 修改垂直滚动条 */
  ::-webkit-scrollbar {
    width: 0.0625rem; /* 修改宽度 */
  }

  /* 修改滚动条轨道背景色 */
  ::-webkit-scrollbar-track {
  }

  /* 修改滚动条滑块颜色 */
  ::-webkit-scrollbar-thumb {
    // background-color: #888;
    background-color: rgba(0, 0, 0, 0.1);
  }

  /* 修改滚动条滑块悬停时的颜色 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  /* 修改滚动条滑块移动时的颜色 */
  ::-webkit-scrollbar-thumb:active {
    background-color: #333;
  }

  /* 修改滚动条滑块的圆角 */
  ::-webkit-scrollbar-thumb {
    border-radius: 0.375rem;
  }
  //水平方向滚动条
  ::-webkit-scrollbar:horizontal {
    height: 0.0625rem;
  }
  ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: black;
  }
}
</style>