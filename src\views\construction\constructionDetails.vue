<template>
  <div class="app" v-loading="loading">
    <backBtn></backBtn>
    <div class="left_box">
      <div class="zhibo_box" v-show="false">
        <img class="zhibo_img" src="https://cdn.juesedao.cn/mdy/49b615d698944a17bb2b5da07d815542" alt="">
        <p class="pp1">正在直播</p>
        <img class="aa_img" src="https://cdn.juesedao.cn/mdy/f7cf995d27f540b39ad7f4c4098a4258" alt="">
      </div>
      <div class="name_address">
        <div class="name">
          <span class="sp1">{{gongdiData.city ? gongdiData.city + ' | ' : ''}}{{gongdiData.community}}</span>
          <span class="sp2">{{gongdiData.scheduleName}}</span>
        </div>
        <div class="address">{{gongdiData.storeName ? gongdiData.storeName + ' | ' : ''}}{{gongdiData.address}}</div>
      </div>
      <!-- 步骤 -->
      <div class="buzuo_box">
        <div class="bu_min_box">
          <div class="box_ss1">
            <div :class="status == '1' ? 'num_box1' : 'num_box'">
              <img v-show="status > 1" class="img_min" src="../../assets/images/yes.png" alt="">
              <p class="num_text" v-show="status < 1">1</p>
              <p class="num_act" v-show="status == 1">1</p>
            </div>
            <div class="title">门店已接单</div>

          </div>
          <div class="box_ss">
            <div :class="status == '2' ? 'num_box1' : 'num_box'">
              <img class="img_min" v-show="status > 2" src="../../assets/images/yes.png" alt="">
              <p class="num_act" v-show="status == 2">2</p>
              <p class="num_text" v-show="status < 2">2</p>
            </div>
            <div class="title">总部确认中</div>
          </div>
          <div class="box_ss">
            <div :class="status == '3' ? 'num_box1' : 'num_box'">
              <img class="img_min" v-show="status > 3" src="../../assets/images/yes.png" alt="">
              <p class="num_act" v-show="status == 3">3</p>
              <p class="num_text" v-show="status < 3">3</p>
            </div>
            <div class="title">施工中</div>
          </div>
          <div class="box_ss">
            <div :class="status == '4' ? 'num_box1' : 'num_box'">
              <img class="img_min" v-show="status > 4" src="../../assets/images/yes.png" alt="">
              <p class="num_act" v-show="status == 4">4</p>
              <p class="num_text" v-show="status < 4">4</p>
            </div>
            <div class="title">施工完成 </div>
          </div>
          <div class="box_ss">
            <div :class="status == '5' ? 'num_box1' : 'num_box'">
              <img class="img_min" v-show="status > 5" src="../../assets/images/yes.png" alt="">
              <p class="num_act" v-show="status == 5">5</p>
              <p class="num_text" v-show="status < 5">5</p>
            </div>
            <div class="title">项目验收中</div>
          </div>
          <div class="box_ss">
            <div :class="status == '6' ? 'num_box1' : 'num_box'">
              <img class="img_min" v-show="status > 6" src="../../assets/images/yes.png" alt="">
              <p class="num_act" v-show="status == 6">6</p>
              <p class="num_text" v-show="status < 6">6</p>
            </div>
            <div class="title">终身质保</div>
          </div>
        </div>
      </div>

      <!-- 选用产品 -->
      <div class="product_box">
        <div class="product_title">选用产品</div>
        <div class="pro_list">
          <div class="pro_box" v-for="(item,index) in gongdiData.product" :key="index" @click="toDetalis(item.productId)">
            <div class="cp_img">
              <img class="img" :src="item.productImage" alt="">
            </div>
            <p class="cp_name ellipsis">{{item.productName}}</p>
            <p class="cp_xinhao ellipsis">型号:{{item.productModel}}</p>
          </div>
        </div>
      </div>
      <!-- 服务团队 -->
      <div class="tema_box">
        <div class="tema_title">服务团队</div>
        <div class="tema_list">
          <div class="user_info" v-for="(item,index) in teamDataList" :key="index">
            <div class="user_img">
              <img class="img" :src="item.photo" alt="">
            </div>
            <div class="name" v-if="item.name">{{item.name}}·{{item.roleName}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="right_box">
      <div class="title_boxc">
        <p class="title_a">工地状态：</p>
      </div>
      <div class="jd_box">
        <div class="box_boxs" v-show="!isNull" v-for="(item,index) in gongdiData.schedules" :key="index">
          <div class="le_box">
            <p class="time_box">{{item.timeRq}}</p>
          </div>
          <div class="ri_box">
            <p class="pc_text">#{{item.content}}</p>
            <div class="daka_img">
              <el-image class="img" v-for="(item1,index1) in item.image" :key="index1" :src="item1" :fit="contain" :preview-src-list="item.image"></el-image>
              <!-- <img class="img" v-for="(item1,index1) in item.image" :key="index1" :src="item1" alt=""> -->
            </div>
            <p class="up_time">{{item.timeFz}} 由{{item.roleName}}·{{item.userName}}上传</p>
          </div>
        </div>

        <div class="null_box" v-show="isNull">
          <el-empty :image-size="200"></el-empty>
        </div>
      </div>

      <!-- <div class="duibi_box">
        <div class="title_b">
          <img class="img" src="../../assets/images/Compare.png" alt="">
          <p class="tt1">工地前后对比</p>
        </div>
        <div class="my_img_box">
          <img class="img" src="https://cdn.juesedao.cn/mdy/0212850d24be47698b8155e6d0617c92" alt="">
          <img class="img" src="https://cdn.juesedao.cn/mdy/0212850d24be47698b8155e6d0617c92" alt="">
        </div>
       
      </div> -->
      <div class="qr_box">
        <div>
          <p class="p1">手机扫码</p>
          <p class="p1">同步查看</p>
        </div>
        <div class="qr_img">
          <el-image class="img" v-for="(item1,index1) in qrArrImg" :key="index1" :src="item1" :fit="contain" :preview-src-list="qrArrImg"></el-image>

          <!-- <img class="img" :src="gongdiData.qrImage" alt=""> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      workId: "", //工地id
      gongdiData: "", //工地数据
      teamDataList: [], //团队人员数据
      loading: false, //是否显示加载
      isNull: false,
      qrArrImg: [],
      status: "5",
      shigongStatus: [
        { value: "1", label: "门店已接单" },
        { value: "2", label: "总部确认中" },
        { value: "3", label: "施工中" },
        { value: "4", label: "施工完成" },
        { value: "5", label: "项目验收中" },
        { value: "6", label: "终身质保" },
      ],
    };
  },
  components: {
    backBtn,
  },
  methods: {
    getGongdiDetails() {
      this.loading = true;
      this.$axios
        .get(`oe_projectDetail_.csp`, {
          params: {
            dbName: "seddelivery",
            workId: this.workId,
          },
        })
        .then((res) => {
          this.loading = false;
          console.log(res);
          if (res.code == "1") {
            res.records.schedules.forEach((item) => {
              if (this.isJson(item.image)) {
                //判断是否为json类型
                item.image = JSON.parse(item.image); //图片json格式解析
                item.roleName = this.setRoleName(item.role); //角色出来
              }
              item.timeRq = item.createTime.split(" ")[0]; //切割获取年月日
              let aTime = item.timeRq.split("-"); //年月日切割单独数组
              item.timeRq = aTime[1] + "-" + aTime[2]; //拼接数组月和日
              item.timeFz = item.createTime.split(" ")[1]; //切割获取时分秒
            });
            if (res.records.schedules.length > 0) {
              this.isNull = false;
            } else {
              this.isNull = true;
            }
            this.shigongStatus.forEach((item) => {
              if (item.value == res.records.schedule) {
                res.records.scheduleName = item.label;
              }
            });
            this.qrArrImg.push(res.records.qrImage);
            this.gongdiData = res.records;
            this.status = res.records.schedule;
          }
        });
    },

    //获取团队人员
    getTeamData() {
      this.$axios
        .get(`oe_projectTeams_.csp`, {
          params: { dbName: "seddelivery", workId: this.workId },
        })
        .then((res) => {
          if (res.code == "1") {
            res.records.forEach((item) => {
              item.roleName = this.setRoleName(item.role);
            });
            let obj = {
              id: "",
              photo: "",
              name: "",
            };
            if (res.records.length % 2) {
              res.records.push(obj);
            }
            console.log(res.records);
            this.teamDataList = res.records;
          }
        });
    },

    toDetalis(id) {
      this.$router.push({ path: "/productDetails", query: { id: id } });
    },

    //角色处理
    setRoleName(role) {
      let roleName = "";
      switch (Number(role)) {
        case 0:
          roleName = "业主";
          break;
        case 1:
          roleName = "经销商";
          break;
        case 2:
          roleName = "店长";
          break;
        case 3:
          roleName = "导购";
          break;
        case 4:
          roleName = "总部监理";
          break;
        case 5:
          roleName = "交付管家";
          break;
        case 6:
          roleName = "瓦工";
          break;
        case 7:
          roleName = "门店设计师";
          break;
        case 8:
          roleName = "分销商";
          break;
        case 9:
          roleName = "外部设计师";
          break;
      }
      return roleName;
    },

    //判断是否为json类型

    isJson(item) {
      if (typeof item !== "string") {
        return false;
      }
      try {
        JSON.parse(item);
        return true;
      } catch (e) {
        return false;
      }
    },
  },
  created() {
    this.workId = this.$route.query.id;
    //获取详情
    this.getGongdiDetails();
    this.getTeamData();
  },
};
</script>

<style lang="scss" scoped>
.app {
  width: 100%;
  height: 100%;
  display: flex;
  .ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .left_box {
    width: 28%;
    min-height: 100vh;
    max-height: 100vh;
    background: #434343;
    overflow-y: auto;
    .zhibo_box {
      width: 2rem;
      height: 0.875rem;
      background: rgba(255, 255, 255, 0.67);
      border-radius: 0 0 0.4375rem 0;
      display: flex;
      align-items: center;
      //   justify-content: center;
      padding-left: 0.125rem;
      box-sizing: border-box;
      .zhibo_img {
        width: 0.375rem;
        // height: 3.5rem;
      }
      .pp1 {
        font-size: 0.1563rem;
        font-weight: bold;
        color: #ff5a5a;
        margin: 0 0.1875rem 0 0.125rem;
      }
      .aa_img {
        width: 0.175rem;
        height: 0.175rem;
      }
    }
    .name_address {
      color: white;
      width: 100%;
      padding: 0 0.0625rem 0 0.125rem;
      box-sizing: border-box;
      margin: 0.1875rem 0;
      .name {
        width: 100%;
        display: flex;
        align-items: center;
        .sp1 {
          font-size: 0.25rem;
          font-weight: bold;
        }
        .sp2 {
          // height: 0.3125rem;
          display: inline-block;
          font-size: 0.0938rem;
          font-weight: bold;
          padding: 0.0625rem 0.125rem;
          border-radius: 1.875rem;
          background: #3783f5;
          margin-left: 0.0625rem;
        }
      }
      .address {
        font-size: 0.1rem;
        margin-top: 0.0625rem;
      }
    }
    .buzuo_box {
      width: 100%;
      height: 1rem;
      background: #3a3a3a;
      padding: 0 0.125rem 0 0.125rem;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      margin: 0.0625rem 0;
      .bu_min_box {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        .box_ss {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          position: relative;
          ::after {
            content: "";
            width: 0.2188rem;
            // height: 0.0625rem;
            border: 0.0219rem dashed #5a5a5a;
            position: absolute;
            left: -0.1375rem;
            top: 0.2188rem;
            transform: translateY(-50%);
          }

          .num_box {
            width: 0.3125rem;
            height: 0.3125rem;
            border: 0.0313rem solid #c1c6ce;
            box-sizing: border-box;
            border-radius: 0.3125rem;
            display: flex;
            align-items: center;
            justify-content: center;

            .img_min {
              width: 56%;
            }
            .num_text {
              font-size: 0.1875rem;
              font-weight: bold;
              color: #b9bdc4;
            }
            .num_act {
              font-size: 0.1875rem;
              font-weight: bold;
              color: #0b131d;
            }
          }
          .num_box1 {
            width: 0.3125rem;
            height: 0.3125rem;
            border: 0.0625rem solid #fff;
            box-sizing: border-box;
            border-radius: 3.125rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;

            .num_act {
              font-size: 0.1875rem;
              font-weight: bold;
              color: #0b131d;
            }
            .num_text {
              font-size: 0.1875rem;
              font-weight: bold;
              color: #b9bdc4;
            }
          }
          .title {
            color: #c1c6ce;
            font-size: 0.0938rem;
            margin-top: 0.0625rem;
          }
        }
        .box_ss1 {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          position: relative;
          .num_box {
            width: 0.3125rem;
            height: 0.3125rem;
            border: 0.0313rem solid #c1c6ce;
            box-sizing: border-box;
            border-radius: 0.3125rem;
            display: flex;
            align-items: center;
            justify-content: center;

            .img_min {
              width: 56%;
            }
            .num_text {
              font-size: 0.1875rem;
              font-weight: bold;
              color: #b9bdc4;
            }
          }
          .num_box1 {
            width: 0.4375rem;
            height: 0.4375rem;
            border: 0.0625rem solid #fff;
            box-sizing: border-box;
            border-radius: 3.125rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;

            .num_act {
              font-size: 0.1875rem;
              font-weight: bold;
              color: #0b131d;
            }
          }
          .title {
            color: #c1c6ce;
            font-size: 0.0938rem;
            margin-top: 0.0625rem;
          }
        }
      }
    }

    .product_box {
      width: 100%;
      //   height: 16.25rem;
      padding: 0 0.0625rem 0 0.125rem;
      box-sizing: border-box;
      margin: 0.1875rem 0;
      .product_title {
        font-size: 0.1563rem;
        font-weight: bold;
        color: white;
      }
      .pro_list {
        width: 100%;
        height: 2.25rem;
        margin-top: 0.1rem;
        display: flex;
        overflow-y: auto;
        .pro_box {
          width: 0.9375rem;
          height: 100%;
          color: white;
          margin-right: 0.1125rem;
          .cp_img {
            width: 100%;
            height: 1.875rem;
            .img {
              width: 100%;
              height: 100%;
            }
          }
          .cp_name {
            font-size: 0.125rem;
            font-weight: bold;
            margin: 0.0187rem 0;
          }
          .cp_xinhao {
            font-size: 0.0938rem;
          }
        }
      }
    }

    // 服务团队
    .tema_box {
      width: 100%;
      margin-top: 0.125rem;
      .tema_title {
        padding: 0 0.125rem;
        box-sizing: border-box;
        color: white;
        font-size: 0.1563rem;
        font-weight: bold;
      }
      .tema_list {
        width: 100%;
        margin-top: 0.0625rem;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        .user_info {
          width: 100%;
          height: 0.5rem;
          background: #5a5a5a;
          margin: 0.0625rem 0;
          padding: 0 0 0 0.125rem;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          .user_img {
            width: 0.4063rem;
            height: 0.4063rem;
            border-radius: 0.625rem;
            overflow: hidden;
            .img {
              width: 100%;
            }
          }
          .name {
            font-size: 0.1125rem;
            color: white;
            margin-left: 0.0625rem;
          }
        }
      }
    }
  }

  .right_box {
    width: 75%;
    min-height: 100vh;
    max-height: 100vh;
    background: radial-gradient(
      50% 50% at 62.24% 50%,
      #fff 28.63%,
      #f6f5f2 100%
    );
    .title_boxc {
      width: 89%;
      height: 0.625rem;
      margin: 0 auto;
      display: flex;
      align-items: flex-end;
      .title_a {
        font-size: 0.25rem;
        font-weight: bold;
      }
    }
    .jd_box {
      width: 89%;
      min-height: 86vh;
      max-height: 86vh;
      margin: 0.4375rem auto 0;
      padding-right: 0.125rem;
      overflow-y: auto;
      .box_boxs {
        width: 100%;
        display: flex;
        margin-bottom: 0.3125rem;
        .le_box {
          width: 12%;
          display: flex;
          justify-items: flex-end;
          .time_box {
            width: 100%;
            font-size: 0.2188rem;
            font-weight: bold;
            padding-left: 0.0938rem;
            box-sizing: border-box;
          }
        }
        .ri_box {
          width: 88%;
          .pc_text {
            font-size: 0.1875rem;
          }
          .daka_img {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.125rem;
            margin-top: 0.0625rem;
            .img {
              width: 100%;
              height: 100%;
            }
          }
          .up_time {
            font-size: 0.1437rem;
            color: #323232;
            margin-top: 0.125rem;
          }
        }
      }
    }

    // 对比
    .duibi_box {
      width: 2.25rem;
      height: 1.375rem;
      border-radius: 0.3688rem 0 0 0;
      position: fixed;
      right: 0;
      bottom: 0;
      background: white;
      padding: 0.125rem 0.125rem 0.125rem 0.1875rem;
      box-sizing: border-box;
      .title_b {
        width: 100%;
        display: flex;
        align-items: center;
        .img {
          width: 0.1875rem;
          height: 0.1875rem;
          margin-right: 0.0938rem;
        }
        .tt1 {
          font-size: 0.125rem;
          color: #2d2d2d;
        }
      }
      .my_img_box {
        width: 100%;
        margin-top: 0.0625rem;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.0938rem;
        .img {
          width: 0.875rem;
          height: 0.875rem;
          cursor: pointer;
        }
      }
    }
  }

  .qr_box {
    width: 1.5625rem;
    // height: 1.5625rem;
    position: fixed;
    top: 0.0625rem;
    right: 0.0625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    // background: radial-gradient(
    //   50% 50% at 62.24% 50%,
    //   #d3e4ff 0%,
    //   #e7effd 100%
    // );
    .p1 {
      font-size: 0.125rem;
      color: #3f3e3e;
      margin-right: 0.0625rem;
    }
    .qr_img {
      width: 0.9375rem;
      height: 0.9375rem;
      border-radius: 0.75rem;
      overflow: hidden;
      .img {
        width: 100%;
      }
    }
  }

  /* 自定义滚动条整体样式 */
  ::-webkit-scrollbar {
    width: 0.0625rem !important; /* 设置滚动条的宽度 */
    height: 0.0625rem;
  }

  /* 自定义滚动条滑块样式 */
  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.27); /* 设置滑块的颜色 */
    border-radius: 0.3125rem;
  }

  /* 自定义滚动条轨道样式 */
  ::-webkit-scrollbar-track {
    // background-color: rgba(255, 255, 255, 0.67); /* 设置轨道的颜色 */
    border-radius: 0.3125rem;
  }

  .null_box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  ::v-deep .el-empty__description p {
    font-size: 0.125rem !important;
  }
  ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: black;
  }
}
</style>
