<template>
  <div class="app" v-loading="loading">
    <backBtn></backBtn>
    <div class="top_box">
      <div class="left_box1">
        <p class="time_zt">{{time_show}}</p>
        <p class="box1">
          <span class="rq_zt">{{date_show}}</span>
          <span class="time_xq">{{week}}</span>
        </p>
      </div>
      <div class="zhong_box1">施恩德品牌营销数据大屏</div>
      <div class="left_box1">
        <img class="sy_img" @click="toFunBtn(1)" src="https://cdn.juesedao.cn/mdy/6a5e0664984847388240ed30bf8b3815" alt="">
        <img class="sy_img" @click="toFunBtn(2)" src="https://cdn.juesedao.cn/mdy/acd67a9a51af45888cce45dcce6d6259" alt="">
        <img class="sy_img" @click="toFunBtn(3)" src="https://cdn.juesedao.cn/mdy/14ced61767a041499e6d08aae604ea06" alt="">
      </div>
    </div>

    <div class="big_box">
      <div class="b_box1">
        <div class="top_1">
          <div class="img1">
            <img class="img" src="https://cdn.juesedao.cn/mdy/d70788c46f0248c58e878ad82927b8aa" alt="">
            <div class="num_box">
              <ICountUp :delay="delay" :endVal="tongjiData.objValue.users ?tongjiData.objValue.users : 0" :options="options" />
            </div>
          </div>
          <div class="img1">
            <img class="img" src="https://cdn.juesedao.cn/mdy/7f08b874e7f748e5832a57b3df92ae7f" alt="">
            <div class="num_box">
              <ICountUp :delay="delay" :endVal="tongjiData.objValue.all_project" :options="options" />
            </div>
          </div>
        </div>
        <div class="echatr_box">
          <div class="e_titlte">
            <p class="p1">工地动态阶段分布</p>
          </div>
          <!-- <div id="barChart" class="echatr_box1"></div> -->
          <div id="echart" ref="chartDom"></div>
        </div>
        <div class="pai_box1">
          <div class="e_titlte">
            <p class="p1">全国省市工地排行榜</p>
          </div>
          <div class="tabbar_box">
            <div class="box_ss">
              <div class="b_1">排名</div>
              <div class="b_1">区域</div>
              <div class="b_1">总数</div>
              <div class="b_1">已完成</div>
            </div>
            <div class="big_box1">
              <div class="box_ss" v-for="(item,index) in tongjiData.projectNum" :key="index">
                <div class="b_1">
                  <img class="pai_img" v-show="index == 0" src="https://cdn.juesedao.cn/mdy/a95cb3f63dca46cd815de1c7d3afd82b" alt="">
                  <img class="pai_img" v-show="index == 1" src="https://cdn.juesedao.cn/mdy/12a3413a89a34d9ca712eaab755368a6" alt="">
                  <img class="pai_img" v-show="index == 2" src="https://cdn.juesedao.cn/mdy/e46b5075b6b34e428d00a5cfff9664be" alt="">
                  <!-- <img class="pai_img" v-show="index == 3" src="https://cdn.juesedao.cn/mdy/bcaedd62207d412192a4028a284dfac1" alt="">
                  <img class="pai_img" v-show="index == 4" src="https://cdn.juesedao.cn/mdy/1177319970d841b7b21c8e9a7b57146b" alt=""> -->
                  <span class="index_box" v-show="index != 0 && index != 1 && index != 2">{{index+1}}</span>
                </div>
                <div class="b_1">{{item.name}}</div>
                <div class="b_1">{{item.value}}</div>
                <div class="b_1">0</div>
              </div>
            </div>

          </div>
        </div>

      </div>
      <div class="b_box1">
        <div class="top_1">
          <div class="img1">
            <img class="img" src="https://cdn.juesedao.cn/mdy/e7c101ba73bc4a72834f5e4448375d16" alt="">
            <div class="num_box">
              <ICountUp :delay="delay" :endVal="tongjiData.objValue.zaijian" :options="options" />
            </div>
          </div>
          <div class="img1">
            <img class="img" src="https://cdn.juesedao.cn/mdy/9fa25c12b621424d9f7e638a20906039" alt="">
            <div class="num_box">
              <ICountUp :delay="delay" :endVal="tongjiData.objValue.store" :options="options" />
            </div>
          </div>
        </div>

        <div class="ex_box1">
          <div class="e_titlte">
            <p class="p1">工地贡献榜</p>
          </div>
          <div class="jdu_box1">
            <div class="jindu_boxs" v-for="(item,index) in tongjiData.projectStoreNum" :key="index" v-if="index < 10">
              <div class="t_box">
                <div class="left">
                  <span class="sp1" :class="index+1 == '1' ? 'color3' : index+1  == '2' ? 'color4' : index+1  == '3' ? 'color5' : 'color6'">No.{{index+1}}</span>
                  <span class="sp2">{{item.storeName}}</span>
                </div>
                <div class="right">{{item.num}}</div>
              </div>
              <div>
                <!-- <el-progress :percentage="item.storeName" style="background: linear-gradient(to right, #073240, #19a2d1);" :show-text="false" define-back-color="#0D536B" color="#fff"></el-progress> -->
                <el-progress :percentage="item.num" :show-text="false" define-back-color="#0D536B" color="#fff"></el-progress>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="b_box1">
        <div class="top_1">
          <div class="img1">
            <img class="img" src="https://cdn.juesedao.cn/mdy/6b8d0c4e382e4ccc8f7f65f81f922747" alt="">
            <div class="num_box">
              <ICountUp :delay="delay" :endVal="tongjiData.objValue.yiwangong" :options="options" />
            </div>
          </div>
          <div class="img1">
            <img class="img" src="https://cdn.juesedao.cn/mdy/7aa258a5b4c446d09f1213a9b01528f8" alt="">
            <div class="num_box">
              <ICountUp :delay="delay" :endVal="tongjiData.objValue.waiter" :options="options" />
            </div>
          </div>
        </div>

        <!--  -->
        <div class="shuju_gl">
          <div class="title_box">
            <img class="t_img" src="https://cdn.juesedao.cn/mdy/6df1547bc06f4326923794a86870b7d1" alt="">
            <div class="title_ts">数据管理</div>
          </div>
          <div class="c_box">

            <div class="con_boxs" v-for="(item,index) in tongjiData.productAndCase" :key="index">
              <div class="c_1" style="justify-content: start;">
                <div class="c_2">
                  <span class="s1">{{item.name}}数量</span>
                  <span class="s2 color1">
                    <ICountUp :delay="delay" :endVal="item.count1" :options="options" />
                  </span>
                </div>
              </div>
              <div class="c_1" style="justify-content: center;">
                <div class="c_2">
                  <span class="s1">上线{{item.name}}</span>
                  <span class="s2 color2">
                    <ICountUp :delay="delay" :endVal="item.count2" :options="options" />
                  </span>
                </div>
              </div>
              <div class="c_1" style="justify-content: flex-end;">
                <div class="c_2">
                  <span class="s1">未上线</span>
                  <span class="s2">
                    <ICountUp :delay="delay" :endVal="item.count3" :options="options" />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 产品统计 -->
        <div class="prodct_box">
          <div class="title_box">
            <p class="p1">产品浏览排行统计</p>
          </div>
          <div class="pro_list">
            <div class="box_s" v-for="(item,index) in productList" :key="index">
              <div class="pro_img">
                <el-image class="img" :src="item.productUrl2" fit="cover"></el-image>
                <!-- <img class="img" src="https://cdn.juesedao.cn/mdy/efb51384736248158a2c89eeb7f2361e" alt=""> -->
              </div>
              <div class="con_box">
                <div class="name">{{item.name}}</div>
                <div class="guige">规格：{{item.sizename}}</div>
                <div class="xinhao">型号：{{item.productModel}}</div>
                <div class="num_box">
                  <!-- <img class="time_img" src="https://cdn.juesedao.cn/mdy/038ae9ebce124bb897071a5a5881d710" alt=""> -->
                  <p class="pp">
                    <ICountUp :delay="delay" :endVal="item.count" :options="options" />

                    次
                  </p>
                </div>
              </div>
              <div class="paiming_box">
                <div class="num_box1" :class="index+1 == '1' ? 'color3' :  index+1 == '2' ? 'color4' :   index+1 == '3' ? 'color5' :'color6'">NO.{{index+1}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import echarts from "echarts";
import * as echarts from "echarts";
import ICountUp from "vue-countup-v2";
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      productList: [], //产品名称
      tongjiData: {
        objValue: {
          users: 0,
          all_project: 0,
          zaijian: 0,
          yiwangong: 0,
          waiter: 0,
          store: 0,
        },
      }, //所有统计数据
      loading: false,
      delay: 0.3,
      endVal: 0,
      options: {
        useEasing: false,
        useGrouping: false,
        separator: ",",
        decimal: ".",
        prefix: "",
        suffix: "",
      },
      week: "", // 本周周几
      date_show: "", //本日日期
      time_show: "", //当前时间，时分秒
      options1: "",
      timeNull: null,
      myChart: null,
    };
  },
  components: {
    ICountUp,
    backBtn,
  },

  methods: {
    //获取产品排名数据
    getProductList() {
      this.$axios
        .get(`oe_vProductRankList_.csp`, { params: { dbName: "schender" } })
        .then((res) => {
          if (res.code == "1") {
            this.productList = res.records;
          }
        });
    },

    //获取统计数据
    getTongjiData() {
      this.loading = true;
      this.$axios
        .get(`oe_getDataForPing2_.csp`, { params: { dbName: "seddelivery" } })
        .then((res) => {
          this.loading = false;
          if (res.code == "1") {
            //排序
            let aa = res.projectNum.sort((a, b) => {
              return b.value - a.value;
            });
            res.objValue = {
              users: Number(res.users),
              all_project: Number(res.all_project),
              zaijian: Number(res.zaijian),
              yiwangong: Number(res.yiwangong),
              store: Number(res.store),
              waiter: Number(res.waiter),
            };
            this.initData(res.projectStatusNum);
            this.tongjiData = res;
          }
        });
    },

    //点击功能按键
    toFunBtn(e) {
      if (e == "1") {
        return false;
      } else if (e == "2") {
        this.$router.go(0);
        return false;
      } else if (e == "3") {
        this.$router.go(-1);
        return false;
      }
    },

    //获取实时时间
    // 获取当前系统日期
    getdataTime() {
      let wk = new Date().getDay();
      let yy = new Date().getFullYear();
      let mm = new Date().getMonth() + 1;
      let dd = new Date().getDate();
      let weeks = [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
      ];
      this.week = weeks[wk];
      this.date_show = yy + "年" + mm + "月" + dd + "日";
    },
    // 获取当前系统的时间
    getnewTime() {
      let hh = new Date().getHours();
      let mf =
        new Date().getMinutes() < 10
          ? "0" + new Date().getMinutes()
          : new Date().getMinutes();
      let ss =
        new Date().getSeconds() < 10
          ? "0" + new Date().getSeconds()
          : new Date().getSeconds();
      this.time_show = hh + ":" + mf + ":" + ss;
    },

    //init
    initData(datas) {
      let bData = datas.map((b) => b.name);
      let tData = datas.map((t) => t.count);
      this.myChart = echarts.init(this.$refs.chartDom);
      // const chartContainer = document.getElementById("barChart");

      const options1 = {
        tooltip: {
          show: true,
        },
        legend: {
          textStyle: {
            fontSize: 10,
            show: false,
          },
        },
        xAxis: {
          data: bData,
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff",
            },
          },
          axisLine: {
            lineStyle: {
              color: "#094060",
            },
          },
        },
        yAxis: {
          axisLine: {
            lineStyle: {
              color: "#094060",
            },
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff",
            },
          },
          splitLine: {
            lineStyle: {
              color: ["#07405c"],
            },
          },
        },
        itemStyle: {
          normal: {
            color: function (params) {
              var colorList = ["#1FC6FF"];
              return colorList[params.dataIndex % colorList.length];
            },
          },
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
        series: [
          {
            type: "bar",
            barWidth: 20,
            data: tData,
          },
        ],
      };

      // 设置图表选项
      this.myChart.setOption(options1);

      window.addEventListener("resize", () => {
        // 第六步，执行echarts自带的resize方法，即可做到让echarts图表自适应
        this.myChart.resize();
        // 如果有多个echarts，就在这里执行多个echarts实例的resize方法,不过一般要做组件化开发，即一个.vue文件只会放置一个echarts实例
        /*
        this.myChart2.resize();
        this.myChart3.resize();
        ......
        */
      });
    },
  },
  mounted() {
    //获取统计数据
    this.getProductList();
    this.getTongjiData();
    this.initData();
    this.beforeDestroy();
    //获取产品数据
    // 获取日期-星期
    this.getdataTime();
    // 读秒
    this.timeNull = setInterval(() => {
      this.getnewTime();
    }, 1000);
  },
  beforeUnmount() {
    clearInterval(this.timeNull);
  },
  beforeDestroy() {
    /* 页面组件销毁的时候，别忘了移除绑定的监听resize事件，否则的话，多渲染几次
      容易导致内存泄漏和额外CPU或GPU占用哦 */
    window.removeEventListener("resize", () => {
      this.myChart.resize();
    });
  },
};
</script>

<style lang="scss" scoped>
.app {
  min-width: 100vw;
  min-height: 100vh;
  background: #041a21;
  .top_box {
    width: 100%;
    height: 0.5625rem;
    background: url(https://cdn.juesedao.cn/mdy/e5551492b89946d682cc8f2d70a7979d)
      no-repeat;
    background-size: 100% 100%;
    padding: 0 0.0313rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    overflow: hidden;
    .left_box1 {
      width: 3.625rem;
      height: 100%;
      color: #1fc6ff;
      display: flex;
      align-items: center;
      padding-left: 0.25rem;
      box-sizing: border-box;

      .time_zt {
        font-size: 0.125rem;
        font-weight: bold;
        margin-right: 0.25rem;
        // position: relative;
        // ::after {
        //   content: "";
        //   width: 0.125rem;
        //   height: 0.625rem;
        //   position: absolute;
        //   top: 0;
        //   right: 0;
        //   background: red;
        // }
      }
      .box1 {
        font-size: 0.0938rem;
        color: #1fc6ff;
        position: relative;
        ::after {
          content: "";
          width: 0.0063rem;
          height: 0.125rem;
          background: #135280;
          position: absolute;
          left: -0.125rem;
          top: 50%;
          transform: translateY(-50%);
        }
        .rq_zt {
          margin-right: 0.125rem;
        }
      }
      .sy_img {
        width: 0.6875rem;
        margin-right: 0.125rem;
      }
    }
    .zhong_box1 {
      width: 8.4375rem;
      height: 100%;
      color: white;
      margin: 0 0.125rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.25rem;
      letter-spacing: 0.0125rem;
      font-weight: bold;
    }
  }

  .big_box {
    width: 100%;
    height: 100%;
    // display: grid;
    // grid-template-columns: repeat(3, 1fr);
    // gap: 0.25rem;
    display: flex;
    justify-content: space-between;
    padding: 0 0.1875rem;
    box-sizing: border-box;
    margin-top: 0.3125rem;
    .b_box1 {
      width: 32.5%;
      height: 100%;
      .top_1 {
        width: 100%;
        // display: grid;
        // grid-template-columns: repeat(2, 1fr);
        display: flex;
        justify-content: space-between;
        .img1 {
          width: 48%;
          position: relative;

          .img {
            width: 100%;
            position: absolute;
            left: 0;
            top: 0;
          }
          .num_box {
            width: 100%;
            height: 100%;
            align-items: center;
            padding: 0 0.125rem;
            box-sizing: border-box;
            span {
              display: flex;
              align-items: center;
              font-size: 0.2375rem;
              color: #4fd7f5;
              font-weight: bold;
              display: flex;
              line-height: 1.3125rem;
              font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            }
          }
        }
      }

      .shuju_gl {
        width: 100%;
        .title_box {
          height: 0.375rem;
          position: relative;
          padding: 0 0.1563rem;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          .t_img {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
          }
          .title_ts {
            font-size: 0.1563rem;
            font-weight: bold;
            color: white;
          }
        }

        .c_box {
          width: 100%;
          background: url(https://cdn.juesedao.cn/mdy/6df1547bc06f4326923794a86870b7d1)
            no-repeat;
          background-size: 100% 100%;
          margin-top: 0;
          .con_boxs {
            width: 100%;
            height: 0.625rem;
            border-bottom: 0.0063rem solid;
            border-image: linear-gradient(
                90deg,
                rgba(31, 199, 255, 0) 0%,
                #1fc7ff 50.42%,
                rgba(31, 199, 255, 0) 94.9%
              )
              1;
            padding: 0.125rem 0.1875rem;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            .c_1 {
              width: 33%;
              height: 100%;
              color: white;
              display: flex;
              align-items: center;
              .c_2 {
                height: 100%;
                display: flex;
                flex-direction: column;
                // max-width: 0.5625rem;
                align-items: center;
                justify-content: space-between;
                .s1 {
                  font-size: 0.1313rem;
                  font-weight: bold;
                }
                .s2 {
                  font-size: 0.1225rem;
                  font-weight: bold;
                }
                .color1 {
                  color: #1fc6ff;
                }
                .color2 {
                  color: #f3e54f;
                }
              }
            }
          }
        }
      }

      .prodct_box {
        width: 100%;
        // height: 3.625rem;
        margin-top: 0.125rem;
        .title_box {
          width: 100%;
          height: 0.5rem;
          background: url(https://cdn.juesedao.cn/mdy/e3acd99dbe4043d588d703e037835eaf)
            no-repeat;
          background-size: 100% 100%;
          padding: 0 0.1563rem;
          box-sizing: border-box;
          display: flex;
          align-items: center;

          .p1 {
            font-size: 0.1563rem;
            font-weight: bold;
            color: white;
          }
        }
        .pro_list {
          width: 100%;
          height: 4.375rem;
          overflow-y: auto;

          .box_s {
            width: 100%;
            min-height: 0.625rem;
            background: url(https://cdn.juesedao.cn/mdy/44da8df652f741928935736fa3fa8cdd)
              no-repeat;
            background-size: 100% 100%;
            padding: 0.0625rem 0.125rem;
            box-sizing: border-box;
            display: flex;
            overflow: hidden;
            border-bottom: 0.0063rem solid;
            border-image: linear-gradient(
                90deg,
                rgba(31, 199, 255, 0) 0%,
                #1fc7ff 50.42%,
                rgba(31, 199, 255, 0) 94.9%
              )
              1;
            // align-items: center;
            .pro_img {
              width: 0.9375rem;
              height: 1.4375rem;
              margin-right: 0.125rem;
              .img {
                width: 100%;
                height: 100%;
              }
            }
            .con_box {
              width: 3.25rem;
              height: 1.4375rem;
              padding: 0.0625rem 0;
              box-sizing: border-box;
              color: white;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              .name {
                font-size: 0.1375rem;
                font-weight: bold;
              }
              .guige {
                font-size: 0.1125rem;
                color: #c2c2c2;
              }
              .xinhao {
                font-size: 0.1187rem;
              }
              .num_box {
                width: 1rem;
                // height: 0.25rem;
                background: url(https://cdn.juesedao.cn/mdy/71745ffe216249b7bf45d259bc66f2e3)
                  no-repeat;
                background-size: 100%;
                padding: 0.0625rem 0.125rem 0.0625rem 0.3125rem;
                .time_img {
                  width: 0.125rem;
                  // height: 0.75rem;
                }
                .pp {
                  font-size: 0.1187rem;
                }
              }
            }

            .paiming_box {
              width: 0.375rem;
              height: 100%;
              color: #1fc7ff;
              font-weight: bold;
              font-size: 0.125rem;
              display: flex;
              justify-content: flex-end;
              padding-top: 0.125rem;
              box-sizing: border-box;
              font-style: italic;
            }
          }
        }
      }

      .ex_box1 {
        width: 100%;
        height: 2.5rem;
        // background: lime;
        .e_titlte {
          width: 100%;
          height: 0.375rem;
          background: url(https://cdn.juesedao.cn/mdy/6df1547bc06f4326923794a86870b7d1)
            no-repeat;
          background-size: 100% 100%;
          padding: 0 0.125rem;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          .p1 {
            font-size: 0.1563rem;
            color: white;
            font-weight: bold;
          }
        }
        .jdu_box1 {
          width: 100%;
          .jindu_boxs {
            width: 100%;
            height: 0.6256rem;
            // background: pink;
            padding: 0.0625rem 0.125rem;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            .t_box {
              width: 100%;
              display: flex;
              justify-content: space-between;
              .left {
                font-size: 0.125rem;
                color: white;
                font-style: italic;
                .sp1 {
                  font-size: 0.125rem;
                  font-weight: bold;
                  margin-right: 0.1rem;
                }
                .sp2 {
                  font-weight: bold;
                }
              }
              .right {
                font-size: 0.125rem;
                font-weight: bold;
                color: #4fd7f5;
              }
            }
          }
        }
      }
      .color3 {
        color: #24dff8;
      }
      .color4 {
        color: #33dfb1;
      }
      .color5 {
        color: #f8cd23;
      }
      .color6 {
        color: #727f83;
      }

      .pai_box1 {
        width: 100%;
        height: 2.75rem;
        .e_titlte {
          width: 100%;
          height: 0.375rem;
          background: url(https://cdn.juesedao.cn/mdy/6df1547bc06f4326923794a86870b7d1)
            no-repeat;
          background-size: 100% 100%;
          padding: 0 0.125rem;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          .p1 {
            font-size: 0.1563rem;
            color: white;
            font-weight: bold;
          }
        }
        .tabbar_box {
          width: 100%;
          height: 3.125rem;

          .big_box1 {
            width: 100%;
            height: 100%;
            background: url(https://cdn.juesedao.cn/mdy/44da8df652f741928935736fa3fa8cdd)
              no-repeat;
            background-size: 100% 100%;
            overflow-y: auto;
          }
          .box_ss {
            width: 100%;
            height: 0.4688rem;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.125rem;
            padding: 0 0.0625rem;
            box-sizing: border-box;

            .b_1 {
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 0.125rem;
              color: white;
              .pai_img {
                width: 0.25rem;
              }
              .index_box {
                width: 0.25rem;
                height: 0.25rem;
                background: #21c2a3;
                border-radius: 0.625rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.125rem;
              }
            }
          }
        }
      }
    }
  }
  .echatr_box {
    width: 100%;
    height: 2.625rem;
    .e_titlte {
      width: 100%;
      height: 0.375rem;
      background: url(https://cdn.juesedao.cn/mdy/6df1547bc06f4326923794a86870b7d1)
        no-repeat;
      background-size: 100% 100%;
      padding: 0 0.125rem;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      .p1 {
        font-size: 0.1563rem;
        color: white;
        font-weight: bold;
      }
    }
    .echatr_box1 {
      width: 100%;
      height: 2.25rem !important;
    }
  }
  #echart {
    width: 100%;
    height: 34vh;
    margin-top: -0.4375rem;
  }
  ::v-deep .el-progress {
    .el-progress-bar__inner {
      background: linear-gradient(to right, #041a21, #949b9d);
      animation: progress 1s ease;
    }
    .el-progress-bar__inner:first-child {
      background: linear-gradient(to right, #073240, #19a2d1);
    }
  }

  /* 修改垂直滚动条 */
  ::-webkit-scrollbar {
    width: 0.0625rem; /* 修改宽度 */
  }

  /* 修改滚动条轨道背景色 */
  ::-webkit-scrollbar-track {
    background: rgba(234, 239, 241, 0.24);
  }

  /* 修改滚动条滑块颜色 */
  ::-webkit-scrollbar-thumb {
    background: rgba(31, 199, 255, 0.24);
  }

  /* 修改滚动条滑块悬停时的颜色 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  /* 修改滚动条滑块移动时的颜色 */
  ::-webkit-scrollbar-thumb:active {
    // background-color: #333;
  }

  /* 修改滚动条滑块的圆角 */
  ::-webkit-scrollbar-thumb {
    border-radius: 0.375rem;
  }

  // 定义动画
  @keyframes progress {
    from {
      width: 0;
    }
    to {
    }
  }
}
</style>
