import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'home',
    component: () => import('../views/home/<USER>')
  },
  {
    path: '/home1',
    name: 'home1',
    component: () => import('../views/home/<USER>')
  },
  {
    path: '/productList',
    name: 'productList',
    component: () => import('../views/product/product.vue'),

  },
  {
    path: '/caseList',
    name: 'caseList',
    component: () => import('../views/case/case.vue')
  },
  {
    path: '/productDetails',
    name: 'productDetails',
    component: () => import('../views/product/productDetails.vue')
  },
  {
    path: '/relatedCases',
    name: 'relatedCases',
    component: () => import('../views/case/relatedCases.vue')
  },
  {
    path: '/relatedConstructionSites',
    name: 'relatedConstructionSites',
    component: () => import('../views/construction/relatedConstructionSites.vue')
  },
  {
    path: '/serverList',
    name: 'serverList',
    component: () => import('../views/server/serverLisr.vue')
  },
  {
    path: '/constructionList',
    name: 'constructionList',
    component: () => import('../views/construction/constructionList.vue')
  },
  {
    path: '/constructionDetails',
    name: 'constructionDetails',
    component: () => import('../views/construction/constructionDetails.vue')
  },
  {
    path: '/map',
    name: 'map',
    component: () => import('../views/map/map.vue')
  },
  {
    path: '/caseDetails',
    name: 'caseDetails',
    component: () => import('../views/case/caseDetails.vue')
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('../views/dashboard/index.vue')
  },
  {
    path: '/viewWeb',
    name: 'viewWeb',
    component: () => import('../views/viewWeb/index.vue')
  },
  {
    path: '/brand',
    name: 'brand',
    component: () => import('../views/viewWeb/brand.vue')
  },
  {
    path: '/chooseBrand',
    name: 'chooseBrand',
    component: () => import('../views/brand/chooseBrand.vue')
  },
  {
    path: '/chooseDetails',
    name: 'chooseDetails',
    component: () => import('../views/brand/chooseDetails.vue')
  },
  {
    path: '/serverPage',
    name: 'serverPage',
    component: () => import('../views/server/serverPage.vue')
  },
  {
    path: '/servicePlatform',
    name: 'servicePlatform',
    component: () => import('../views/server/servicePlatform.vue')
  },
  {
    path: '/qualityGuarantee',
    name: 'qualityGuarantee',
    component: () => import('../views/server/qualityGuarantee.vue')
  },
  {
    path: '/petrosalLuxury',
    name: 'petrosalLuxury',
    component: () => import('../views/server/petrosalLuxury.vue')
  },
  {
    path: '/qucuiProducts',
    name: 'qucuiProducts',
    component: () => import('../views/product/qucuiProducts.vue')
  },
  {
    path: '/listBrands',
    name: 'listBrands',
    component: () => import('../views/brand/listBrands.vue')
  },
  {
    path: '/brandDetails',
    name: 'brandDetails',
    component: () => import('../views/brand/brandDetails.vue')
  },
  {
    path: '/productOuDetails',
    name: 'productOuDetails',
    component: () => import('../views/brand/productOuDetails.vue')
  },
  {
    path: '/oucuiList',
    name: 'oucuiList',
    component: () => import('../views/product/ouCuiList.vue')
  },
  {
    path: '/PetrosalLuxury1',
    name: 'PetrosalLuxury1',
    component: () => import('../views/product/PetrosalLuxury.vue')
  },
  {
    path: '/endChooseDetails',
    name: 'endChooseDetails',
    component: () => import('../views/brand/endChooseDetails.vue')
  },
  {
    path: '/ysChooseDetails',
    name: 'ysChooseDetails',
    component: () => import('../views/brand/ysChooseDetails.vue')
  },
  {
    path: '/ysProductDetails',
    name: 'ysProductDetails',
    component: () => import('../views/product/ysProductDetails.vue')
  },
]

const router = new VueRouter({
  mode: 'hash',
  base: process.env.BASE_URL,
  routes
})



export default router
