<template>
  <div id="app">
    <nav>
      <router-link to="/"></router-link>
    </nav>
    <router-view />
  </div>
</template>

<style>
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  box-sizing: border-box; /* Ensure padding and borders are included in element sizes */
}
#app {
  font-family: "aaa";
}

::v-deep .el-pagination.is-background .el-pager li {
  margin: 0 0.0625rem !important;
}

::v-deep .el-pagination.is-background .btn-next {
  margin: 0 0.0625rem;
  background: transparent;
  border: 0.0063rem solid #ccc;
  box-sizing: border-box;
  font-weight: none;
  min-width: 0.3125rem;
  border-radius: 0.0313rem;
  height: 0.3125rem;
  line-height: 0.3125rem;
}
::v-deep .btn-prev {
  margin: 0 0.0625rem;
  background: transparent;
  border: 0.0063rem solid #ccc;
  box-sizing: border-box;
  font-weight: none;
  min-width: 0.3125rem;
  border-radius: 0.0313rem;
  height: 0.3125rem;
  line-height: 0.3125rem;
}

::v-deep .el-pager li {
  font-size: 0.125rem;
  background-color: transparent;
  border: 0.0063rem solid #ccc;
  box-sizing: border-box;
  font-weight: none;
  min-width: 0.3125rem;
  border-radius: 0.0313rem;
  height: 0.3125rem;
  line-height: 0.3125rem;
}
::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: black;
}
</style>
