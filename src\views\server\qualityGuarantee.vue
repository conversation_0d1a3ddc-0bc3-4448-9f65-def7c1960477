<template>
  <div class="box">
    <!-- <backBtn></backBtn> -->
    <div class="top_box">
      <img @click="toBack" class="back_img" :src="topData.backImg" alt="">
      <p class="back_title">{{topData.title}}</p>
      <p class="xian"></p>
    </div>
    <div class="back_box" @click="toBack">
      <img class="back_img1" src="../../assets/images/back_icon.png" alt="">
      <p class="t_name">返回</p>
    </div>
    <div class="img_aa_box">
      <img class="logo_img" :src="logoImg" alt="">
      <el-image class="qr_img" :src="qrImg[0]" fit="fit" :preview-src-list="qrImg"></el-image>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      topData: {
        title: "终身服务",
        backImg: "https://cdn.juesedao.cn/mdy/0773f7c61eca4838b25d3de26a4cc584",
      },
      qrImg: ["https://cdn.juesedao.cn/mdy/a3066a07636e47f885e37452a27720bf"],
      logoImg: "https://cdn.juesedao.cn/mdy/9b5a01066b5046d48e8dc98d01af6d07",
    };
  },
  methods: {
    toBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 100vh;
  background: url(https://cdn.juesedao.cn/mdy/0cd1f3124428447ea90ad4b76d45274b)
    no-repeat;
  background-size: 100%, 100%;
  .top_box {
    width: 100%;
    height: 14vh;
    display: flex;
    align-items: center;
    padding: 0.0625rem 0.25rem 0 0.5625rem;
    box-sizing: border-box;
    margin-bottom: 2vh;
    .back_img {
      width: 0.2125rem;
      // height: 0.2188rem;
      cursor: pointer;
    }
    .back_title {
      font-size: 0.2rem;
      color: white;
      margin: 0 0.125rem;
    }
    .xian {
      width: 1px;
      height: 0.1625rem;
      background: white;
      margin-top: 0.0313rem;
    }
  }
  // 返回
  .back_box {
    background: rgba(255, 255, 255, 0.25);
    position: absolute;
    right: 0;
    bottom: 0.9375rem;
    color: white;
    border-radius: 1.25rem 0 0 1.25rem;
    display: flex;
    align-items: center;
    padding: 0.125rem 0.25rem 0.125rem 0.25rem;
    z-index: 2001;
    cursor: pointer;
    .back_img1 {
      width: 0.375rem;
      height: 0.375rem;
    }
    .t_name {
      font-size: 0.1875rem;
      margin-left: 0.125rem;
    }
  }
  .img_aa_box {
    position: absolute;
    top: 0.625rem;
    right: 0.375rem;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    .logo_img {
      width: 1.375rem;
    }
    .qr_img {
      width: 0.8125rem;
      margin-top: 0.3625rem;
    }
  }
}
</style>